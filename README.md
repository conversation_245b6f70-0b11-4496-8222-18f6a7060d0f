# Phynance: The Structure of Strategy
## A First-Principles Approach to Discovering the Dynamics of Active ETFs

**Student:** <PERSON><PERSON><PERSON>  
**Supervisor:** Prof. <PERSON>  
**Date:** July 2025

## Overview

This project implements a novel hybrid system identification pipeline designed to discover interpretable, first-principles laws governing an active ETF's interaction with its market environment. The research directly addresses the **"Residual Diagnostics Puzzle"** identified in prior work: sophisticated Dynamic Factor Stochastic Volatility models successfully capture aggregate market dynamics but fail at the individual asset level, indicating systematic misspecification of agent-specific behavior.

The approach combines **Economic Engineering principles** with modern machine learning techniques to create a transparent **Port-Hamiltonian System (PHS)** model that decomposes portfolio behavior into:
- **Conservative dynamics** (Economic Surplus $H$)
- **Dissipative frictions** ($R$) 
- **External market responses** ($g$)

The ARK Innovation ETF (ARKK) serves as the primary case study for discovering unknown structural laws governing active portfolio management.

## Research Motivation & Core Innovation

### The "Chicken-and-Egg" Problem
Traditional financial modeling faces a fundamental challenge: **we cannot know the physical laws without the state trajectory, but we cannot robustly infer the state trajectory without knowing the physical laws**. This creates an interdependency that prevents discovery of agent-specific dynamics.

### Our Solution: Iterative Discovery Pipeline
The methodology breaks this deadlock through a hybrid **Expectation-Maximization (EM)** approach that:
1. **Bootstraps** initial state estimates (internal reservation prices $p_{agent}$)
2. **Iteratively refines** both latent states and physical laws until convergence
3. **Separates** conservative strategy ($H$) from dissipative frictions ($R$) and market coupling ($g$)

## Two-Phase Validation Methodology

### **Phase 1: Pipeline Development & Validation on Synthetic Data**
**Objective:** Prove methodology can recover true laws in controlled environment

#### **Sub-Phase 1A: Linear Coupled Port-Hamiltonian System (Gateway)**
- **Critical Go/No-Go Checkpoint:** Formal structural identifiability analysis
- **Theoretical Foundation:** Coupled agent-market linear PHS with partial observations
- **Success Criterion:** Unique parameter recovery from noisy data

#### **Sub-Phase 1B: Non-Linear Iterative Discovery (Engine Test)**
- **EM Algorithm Validation:** Complete HNN-PySR-EKF iterative loop testing
- **Ground Truth Recovery:** Known non-linear $H$, symbolic $R(q,p)$, $g(q,p)$
- **Bootstrap Independence:** Consistent convergence across initialization methods

### **Phase 2: Application to Real ARKK Data & Discovery**
**Objective:** Apply validated pipeline to discover unknown, interpretable interaction laws

1. **Market Environment Characterization:** DFSV-BIF extraction of market factors
2. **Iterative Discovery Loop:** EM algorithm on real data
3. **Economic Interpretation:** Analysis of discovered laws and strategy insights

## Technology Stack

- **Core Framework:** Python/JAX for system identification and automatic differentiation
- **Market Engine:** DFSV-BIF (validated framework from previous thesis)
- **Agent Engine:** Port-Hamiltonian Systems with Hamiltonian Neural Networks (HNN)
- **Discovery Engine:** PySR for symbolic regression of interpretable laws
- **State Estimation:** Extended Kalman Filter with automatic differentiation
- **Additional Tools:** JAX/Flax, Diffrax, Optax, Optimistix/Equinox, SymPy

## Project Structure

```
.
├── config/                          # Phase-specific configurations
│   ├── phase1a_linear_coupled.yml   # Gateway validation config
│   ├── phase1b_nonlinear_synthetic.yml # EM loop validation
│   ├── phase2_arkk_real.yml         # Real data discovery
│   └── shared_defaults.yml          # Common parameters
├── data/
│   ├── raw/                         # Original datasets
│   │   ├── market_factors/          # DFSV-BIF market data  
│   │   └── arkk_holdings/           # ETF holdings and prices
│   ├── synthetic/                   # Phase 1 synthetic systems
│   │   ├── phase1a_coupled_linear/  # Agent-market coupling
│   │   └── phase1b_nonlinear/       # Known ground truth
│   ├── processed/                   # Clean, aligned data
│   │   ├── market_state/            # z_market = [f_t, h_t]
│   │   └── agent_observations/      # q_obs, derivatives
│   └── validation/                  # Ground truth for validation
├── models/                          # Saved model artifacts
│   ├── phase1a_validated/           # Linear PHS parameters
│   ├── phase1b_validated/           # EM algorithm components
│   └── phase2_discovered/           # ARKK discovered laws
├── notebooks/                       # Analysis notebooks
│   ├── 01_phase1a_gateway_validation.ipynb
│   ├── 02_phase1b_em_algorithm_validation.ipynb  
│   ├── 03_phase2_market_characterization.ipynb
│   ├── 04_phase2_iterative_discovery.ipynb
│   └── 05_results_analysis_and_interpretation.ipynb
├── src/phynance/                   # Core implementation
│   ├── core/                        # Shared PHS components
│   ├── synthetic/                   # Phase 1 synthetic systems
│   ├── identification/              # System ID algorithms
│   ├── learning/                    # HNN and PySR components
│   ├── market/                      # DFSV-BIF market engine
│   ├── data/                        # Data handling utilities
│   └── analysis/                    # Results interpretation
└── docs/                           # Documentation
    ├── implementation.md            # Detailed timeline
    ├── outline.md                  # Thesis structure
    └── newplan/guide.md            # Complete methodology
```

## Key Design Principles

### **DRY (Don't Repeat Yourself) Architecture**
Components are designed for maximum reuse across phases:
- **Core PHS:** Shared across all phases
- **Identification:** PEM, EKF, bootstrap strategies reused
- **Progressive Complexity:** 1A → 1B → 2 with natural component evolution

### **Modular Pipeline**
Each phase can be run independently with clear interfaces and validation checkpoints.

### **Configuration-Driven**
All experiments controlled through YAML configs enabling reproducible research.

## Setup and Installation

This project uses `uv` for environment and package management.

1. **Install uv**:
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Create and activate environment**:
   ```bash
   uv venv .venv --python 3.11
   source .venv/bin/activate  # Linux/macOS
   ```

3. **Install dependencies**:
   ```bash
   uv pip install -e .[dev]
   ```

## Running the Pipeline

### **Phase 1A: Gateway Validation**
```bash
python scripts/run_phase1a_gateway.py --config config/phase1a_linear_coupled.yml
```

### **Phase 1B: EM Algorithm Validation**  
```bash
python scripts/run_phase1b_validation.py --config config/phase1b_nonlinear_synthetic.yml
```

### **Phase 2: Real Data Discovery**
```bash
python scripts/run_phase2_discovery.py --config config/phase2_arkk_real.yml
```

## Theoretical Foundation

### Economic Engineering to PHS Mapping

| Economic Concept | PHS Component | ETF Interpretation |
|:---|:---|:---|
| **Holdings** | $q(t)$ | Asset quantities/weights |
| **Internal Valuation** | $p(t)$ | Agent's reservation prices (economic momentum) |
| **Economic Surplus** | $H(q,p)$ | ETF's strategic objective function |
| **Transaction Costs** | $R(q,p)$ | Frictions, slippage, market impact |
| **Market Forces** | $g(q,p)u(t)$ | External factor influence |

### Complete PHS Model
$$\frac{dz}{dt} = (J - R(q,p))\nabla H(q,p) + g(q,p)u(t)$$

where:
- $z = [q^T, p^T]^T$ (holdings and internal prices)
- $J = \begin{pmatrix} 0 & I \\ -I & 0 \end{pmatrix}$ (canonical symplectic matrix)
- $u(t) = [f_t^T, h_t^T, u_{flows}^T]^T$ (market factors and fund flows)

## Expected Contributions

### **Scientific Contributions**
1. **Methodological Innovation:** First validated framework for PHS identification in finance
2. **Theoretical Advancement:** Novel solution to latent state problem in portfolio dynamics
3. **Empirical Breakthrough:** Interpretable laws governing active ETF behavior
4. **Interdisciplinary Bridge:** Economic Engineering theory grounded with financial data

### **Practical Impact**  
1. **Cost Attribution:** Quantified understanding of ETF transaction costs
2. **Strategy Insights:** Data-driven revelation of true Economic Surplus function
3. **Risk Management:** Transparent decomposition of conservative vs. dissipative dynamics
4. **Optimization Opportunities:** Clear mathematical framework for strategy improvement

## Key References

- Mendel, J. M. *The Newtonian Mechanics of Demand*
- Hutters, C., & Mendel, J. M. *Economic Circuit Theory: Electrical Network Theory for Dynamical Economic Systems*
- Greydanus, S., et al. (2019). *Hamiltonian Neural Networks*. NeurIPS
- Cranmer, M., et al. (2023). *Interpretable Machine Learning for Science with PySR*

## Documentation

- [`docs/newplan/guide.md`](docs/newplan/guide.md): Complete research methodology and theoretical framework
- [`docs/implementation.md`](docs/implementation.md): Detailed timeline and task breakdown
- [`docs/outline.md`](docs/outline.md): Thesis structure and chapter organization

---

**This project represents a pioneering effort to bridge Economic Engineering theory with financial practice through data-driven discovery of first-principles laws governing complex portfolio behavior.**
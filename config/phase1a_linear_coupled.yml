# Phase 1A: Linear Coupled Port-Hamiltonian System Configuration
# Gateway validation with formal identifiability analysis

phase: "1a"
description: "Linear coupled agent-market PHS with partial observations"

# System parameters
agent_system:
  mass: 1.0
  stiffness: 2.0
  reference_position: 0.5
  friction_diagonal: [0.1, 0.05]
  
market_system:
  mass: 0.8
  stiffness: 1.5
  friction_diagonal: [0.08, 0.03]
  
coupling:
  friction_coupling: 0.02
  input_coupling: [0.1, 0.15]
  
# Data generation
synthetic_data:
  time_horizon: 100.0
  dt: 0.1
  noise_level: 0.01
  initial_conditions:
    agent_q: 0.0
    agent_p: 0.1
    market_q: 0.0
    market_p: 0.05
    
# External inputs
external_inputs:
  type: "sinusoidal_with_noise"
  frequency: 0.1
  amplitude: 0.2
  noise_std: 0.05
  
# Identification settings
identification:
  method: "prediction_error_method"
  optimizer: "lbfgs"
  max_iterations: 500
  tolerance: 1e-6
  
# Identifiability analysis
identifiability:
  method: "transfer_function"
  perturbation_size: 1e-6
  condition_number_threshold: 1e12
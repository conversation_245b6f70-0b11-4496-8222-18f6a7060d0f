# Phase 1B: Non-Linear Iterative Discovery Configuration
# EM algorithm validation on synthetic nonlinear agent system

phase: "1b"
description: "Non-linear agent PHS with iterative HNN-PySR-EKF discovery"

# Ground truth nonlinear system
ground_truth:
  hamiltonian:
    type: "polynomial"
    coefficients:
      k1: 1.0    # 1/2 * k1 * q^2
      m_inv: 2.0 # 1/2 * (1/m) * p^2  
      k2: 0.1    # k2 * q^3
      k3: 0.05   # k3 * q * p^2
      
  friction:
    type: "symbolic"
    expression: "r1 * abs(p) + r2 * p**2"
    parameters:
      r1: 0.1
      r2: 0.02
      
  coupling:
    type: "symbolic" 
    expression: "g0 + g1 * q"
    parameters:
      g0: 0.2
      g1: 0.1

# Data generation
synthetic_data:
  time_horizon: 50.0
  dt: 0.05
  noise_level: 0.02
  initial_conditions:
    q: 0.1
    p: 0.2
  n_trajectories: 5  # Multiple initial conditions
  
# External inputs
external_inputs:
  type: "colored_noise"
  correlation_time: 2.0
  variance: 0.1
  
# Bootstrap strategies
bootstrap:
  methods: ["velocity_based", "spread_based", "random_walk"]
  velocity_inertia_estimate: 1.5
  spread_multiplier: 0.1
  random_walk_variance: 0.05
  
# EM algorithm settings
em_algorithm:
  max_iterations: 20
  convergence_tolerance: 1e-4
  damping_factor: 0.7  # For stability
  
# HNN training
hnn:
  architecture:
    hidden_layers: [64, 64, 32]
    activation: "swish"
  training:
    learning_rate: 1e-3
    max_epochs: 500
    batch_size: 128
    early_stopping_patience: 50
  conservation_check:
    tolerance: 1e-3
    
# PySR settings
pysr:
  populations: 30
  iterations: 100
  binary_operators: ["+", "-", "*", "/", "^"]
  unary_operators: ["abs", "square"]
  complexity_of_variables: 1
  parsimony: 0.01
  
# EKF settings
ekf:
  process_noise_variance: 1e-4
  measurement_noise_variance: 1e-3
  initial_covariance: 1e-2
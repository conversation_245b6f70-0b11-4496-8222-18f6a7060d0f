# Phase 2: Real ARKK Data Discovery Configuration
# Apply validated pipeline to discover unknown laws

phase: "2"
description: "ARKK real data with market-driven discovery"

# Data sources
data_sources:
  arkk_holdings: "data/raw/arkk_holdings/"
  market_prices: "data/raw/market_factors/"
  fund_flows: "data/raw/arkk_holdings/flows.csv"
  vix: "data/raw/market_factors/vix.csv"
  
# Time period
time_period:
  start_date: "2014-01-01"
  end_date: "2025-01-01"
  frequency: "daily"
  
# DFSV-BIF market characterization
market_engine:
  n_factors: 5
  volatility_model: "stochastic_volatility"
  estimation_method: "bellman_information_filter"
  factor_identification: "pca"
  
# Data preprocessing
preprocessing:
  derivative_method: "smoothing_splines"
  smoothing_parameter: 0.01
  outlier_detection: true
  outlier_threshold: 3.0
  missing_data_method: "interpolation"
  
# Bootstrap for real data
bootstrap:
  method: "velocity_based"  # Most robust from Phase 1B
  inertia_estimation: "rolling_volatility_inverse"
  window_size: 20
  
# EM algorithm (inherits from Phase 1B)
em_algorithm:
  max_iterations: 25
  convergence_tolerance: 1e-4
  damping_factor: 0.8
  
# HNN for real data
hnn:
  architecture:
    hidden_layers: [128, 128, 64, 32]  # Larger for real complexity
    activation: "swish"
    dropout_rate: 0.1
  training:
    learning_rate: 5e-4  # Lower for stability
    max_epochs: 1000
    batch_size: 256
    weight_decay: 1e-5
    
# PySR for real data
pysr:
  populations: 50  # More thorough search
  iterations: 200
  binary_operators: ["+", "-", "*", "/", "^", "max", "min"]
  unary_operators: ["abs", "square", "sqrt", "log", "exp"]
  complexity_of_variables: 2  # Allow more complex terms
  parsimony: 0.005  # Less aggressive simplification
  
# Economic constraints
economic_constraints:
  r_positive_semidefinite: true
  h_convexity_check: true
  g_bounded: true
  economic_plausibility_tests: true
  
# Validation
validation:
  out_of_sample_period: 0.2
  cross_validation_folds: 5
  bootstrap_iterations: 100
  
# Results analysis
analysis:
  hamiltonian_visualization: true
  cost_attribution: true
  sensitivity_analysis: true
  regime_identification: true
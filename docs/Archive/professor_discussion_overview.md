# Port-Hamiltonian Modeling of Active ETFs:  Overview

**Student:** <PERSON><PERSON><PERSON>  
**Supervisor:** Prof. <PERSON>  
**Date:** Juli 2025  

## Executive Summary

This thesis proposes to develop an **interpretable, structure-preserving Port-Hamiltonian System (PHS)** for modeling actively managed ETF dynamics, specifically targeting the ARK Innovation ETF (ARKK) as a case study. The approach combines Economic Engineering principles with modern machine learning techniques to create a transparent model that decomposes portfolio behavior into conservative dynamics, dissipative frictions, and external market responses.

## Core Research Motivation

### The Problem
- **Lack of Interpretability**: Current portfolio models are either overly simplified (factor models) or black-box ML approaches that provide no structural insights
- **Missing Dynamic Structure**: Traditional models fail to capture the interplay between idealized strategy objectives, real-world frictions, and external market forces
- **Limited Practical Value**: Existing approaches don't provide actionable insights for cost optimization, risk management, or strategy refinement

### The Opportunity
Port-Hamiltonian Systems offer a **principled mathematical framework** that:
- Guarantees stability of the conservative core (Hamiltonian structure)
- Explicitly models energy dissipation (transaction costs, slippage)
- Incorporates external forcing (market volatility, fund flows) in a structured way
- Provides transparent decomposition of portfolio dynamics

## Theoretical Foundation

### Economic Engineering Mapping to PHS
Building on <PERSON><PERSON>'s Economic Engineering framework, we establish the following PHS-ETF correspondence:

| PHS Component | ETF Interpretation                         | Economic Meaning (EconE Framework)           |
| ------------- | ------------------------------------------ | -------------------------------------------- |
| $q(t)$        | Quantity of each asset held by ETF         | Generalized position (economic "charge")     |
| $p(t)$        | ETF's internal valuation/reservation price* | Canonical momentum (economic "flux linkage") |
| $H(q,p)$      | Total Economic Surplus of ETF strategy     | Hamiltonian (idealized objective function)   |
| $R(q,p)$      | Transaction costs, slippage, market impact | Economic frictions (energy dissipation)      |
| $g(q,p)$      | Input coupling matrix                      | How external forces affect system            |
| $u(t)$        | Alpha signals, VIX, fund flows             | External economic forces/wants               |

### The Complete PHS Model
$$\frac{dz}{dt} = (J - R(q,p))\nabla H(q,p) + g(q,p)u(t)$$

where $z = [q^T, p^T]^T$ and $J$ is the canonical symplectic matrix.

*The choice of $q$ (asset quantities) and $p$ (ETF's internal reservation price) as conjugate variables follows rigorously from Lagrangian mechanics. The canonical momentum $p = \frac{\partial L}{\partial \dot{q}} = m\dot{q}$ represents the agent's internal valuation, not the external market price. This interpretation resolves the conceptual challenge of how an ETF can "own" a price: $p$ is the fund's private reservation price that enables a purely internal Hamiltonian $H(q,p)$. Market interaction occurs through the equations of motion—observable market prices influence $\dot{p}$ via non-conservative terms, creating a clean separation between internal strategy ($H$) and external market dynamics ($R, g, u$).*

## Framework & Technology Stack Justification

### Core Approach: PHS + Python/JAX for Data-Driven System Identification

This research combines **Port-Hamiltonian Systems** as the modeling framework with **Python/JAX** as the implementation stack, driven by the specific requirements of solving the **inverse problem** in Economic Engineering.

### Why Port-Hamiltonian Systems?

**Interpretable Structure**: PHS provides direct economic mapping where $H$ represents Economic Surplus, $R$ models frictions, and $g$ captures external forces. The equation $\dot{z} = (J-R)\nabla H + gu$ enforces principled separation of conservative, dissipative, and external dynamics, enabling targeted identification strategies.

**Physical Guarantees**: Bounded $H$ and positive semi-definite $R$ ensure passivity and stability, preventing unphysical solutions while maintaining economic realism (frictions dissipate surplus, never create it).

**Interdisciplinary Bridge**: Compatible with control theory, directly maps to electrical circuits (SPICE compatibility), and grounded in Hamiltonian mechanics, serving as a universal tool across engineering, economics, and finance.

### Why Python/JAX Instead of SPICE?

**Different Problem Type**: While SPICE excels at **forward simulation** (*"given parameters, what behavior?"*), this thesis addresses the **inverse problem** (*"given behavior, what parameters?"*) requiring fundamentally different architecture.

**System Identification Requirements**:
- **Optimization Loop Architecture**: Simulator must be called iteratively within parameter search (SPICE is self-contained, not designed for external optimization)
- **Automatic Differentiation**: JAX provides essential gradients for efficient parameter identification; SPICE only outputs trajectories. The automatic calculation is essential for complex, possibly nonlinear models.
- **Modular Pipeline**: Building extensible identification engine applicable to various Economic Engineering systems

**Collaborative Positioning**: This work **complements** SPICE models by developing the "diagnostic engine" that determines empirical parameters ($m$, $k$, $R$) needed to make their theoretical "chassis" realistic and data-grounded.

## Methodology: Two-Phase Approach

### Phase 1: Linear PHS Toy Model Development & Validation
**Objective:** Develop and validate the system identification pipeline using a simplified linear PHS model that captures essential ETF dynamics

**Theoretical Foundation:**
- **State Variables:** $z = [q, p]^T$ where $q$ represents ETF holdings and $p$ represents market prices
- **Linear Hamiltonian:** $H(z) = \frac{1}{2}k (q - q_{ref})^2 + \frac{1}{2m}p^2$
  - $k$: tracking error cost (spring constant analogy)
  - $m$: market mass/liquidity (inertia analogy) 
  - $q_{ref}$: strategic baseline holdings
- **Parameter Set:** $\theta = \{m, k, q_{ref}, R_q, R_p\}$ with clear economic interpretations
- **System Matrices:** Linear dissipation $R = \text{diag}(R_q, R_p)$ and input matrix $g = [0, 1]^T$

**Implementation & Validation:**
- **Modular Simulation Architecture:** Design simulator with pluggable backends to support both:
  - Native Python/JAX implementation for direct PHS integration
  - SPICE-compatible interface for model validation and cross-verification
- **Technology Stack:**
  - **JAX**: Automatic differentiation for gradient-based optimization and HNN compatibility
  - **Optimistix/Equinox**: Robust ODE solvers (`solve_ivp`) and optimization algorithms (`L-BFGS-B`)
  - **PySpice/NGSpice**: SPICE circuit simulation interface for compatibility (only )
- **Controlled Simulation:** Generate synthetic trajectories with known $\theta_{true}$ and realistic noise levels
- **Preprocessing Pipeline Validation:** Apply smoothing splines methodology to noisy synthetic data to validate derivative estimation accuracy against known ground truth $\dot{z}_{true}$
- **Prediction Error Method:** Implement optimization-based parameter identification using gradient-based methods with analytical gradients
- **Recovery Analysis:** Quantify parameter estimation accuracy, bias, and confidence intervals
- **Identifiability Analysis:** Conduct formal structural identifiability analysis via transfer function analysis of the LTI state-space form to ensure all model parameters can be uniquely determined
- **Sensitivity Testing:** Assess robustness to noise levels, initial conditions, and data length
- **Cross-Platform Validation:** Verify consistency between native and SPICE simulation results

**Critical Outcomes:**
- Validated identification methodology ready for real data application
- Validated preprocessing pipeline with quantified derivative estimation accuracy
- SPICE-compatible simulation framework enabling collaboration and model comparison
- Understanding of fundamental identification limits and challenges
- Benchmark performance metrics for Phase 2 comparison
- Insights into required data quality and quantity for reliable parameter recovery

### Phase 2: Non-Linear PHS Model for Real ETF Data
**Objective:** Apply and extend the validated pipeline to model real active ETF (ARKK) using non-linear components

**Sub-Phase 2A: Data & HNN Training**
- **Data Acquisition:** ARKK holdings (2014-2025), market prices, alpha signals, VIX, fund flows
- **Preprocessing Pipeline:** Multi-stage approach addressing the critical challenge of derivative estimation from noisy financial data:
  - **Data cleaning**: Handle missing values, timestamp alignment, stationarity checks
  - **Smoothing splines**: Fit continuous analytical functions to state variables $z(t)$ to enable stable derivative calculation
  - **Analytical derivatives**: Calculate $\dot{z}(t)$ directly from fitted splines, avoiding noise amplification from numerical differentiation
  - **Economic validation**: Verify trading volume consistency and market microstructure plausibility
  - **Phase 1 validation**: Test pipeline accuracy using synthetic data with known ground truth derivatives
- **Technology Stack Extension:**
  - **JAX/Flax**: Neural network implementation with automatic differentiation
  - **Diffrax**: Sophisticated ODE/SDE solvers for continuous-time modeling
  - **Optax**: Advanced optimization algorithms for neural network training
- **Latent State Handling:** Address unobservable $p$ through dual-role approach: treat observable market prices as (1) noisy measurements of internal reservation price ($p_{market} = p + \epsilon$) and (2) external forcing terms in $u(t)$ that create spread dynamics
- **HNN Implementation:** Learn non-linear Hamiltonian $H(q,p)$ using market prices as proxy for internal valuations, with identification engine performing joint filtering and decomposition
- **HNN Validation:** Assess conservation properties, economic plausibility, and separation of internal vs. external dynamics

**Sub-Phase 2B: Symbolic Discovery & Assembly**
- **Residual Analysis:** Compute $r = \dot{z}_{observed} - J\nabla H_{HNN}$
- **Technology Stack Extension:**
  - **PySR**: Symbolic regression with evolutionary algorithms and Julia backend
  - **SymPy**: Symbolic mathematics for expression manipulation and validation
- **PySR Discovery:** Find symbolic expressions for $R(q,p)$ and $g(q,p)$ from residuals, with $g$ specifically tasked to discover spread dynamics ($p_{market} - p$) and alpha signal coupling
- **Model Assembly:** Integrate learned components into full PHS model
- **Iterative Refinement:** Address potential HNN contamination through EM-like iterative procedure: (1) Initial conservative assumption ($R=0, g=0$) for $H_0$, (2) PySR discovers $R_1, g_1$ from residuals, (3) "Clean" data by subtracting non-conservative effects: $\dot{z}_{clean} = \dot{z}_{obs} - (-R_1\nabla H_0 + g_1u)$, (4) Re-train HNN on cleaned data for $H_1$, (5) Iterate until parameter and model fit convergence
- **Stochastic Extension:** Estimate diffusion matrix $\Sigma$ from final residuals

**Sub-Phase 2C: Validation & Interpretation**
- **Performance Assessment:** Compare against benchmarks, out-of-sample testing
- **Economic Interpretation:** Analyze learned Hamiltonian landscape, friction structure
- **Insights Extraction:** Cost attribution, strategy analysis, regime identification

## Expected Outcomes & Contributions

### Scientific Contributions
1. **Methodological Innovation**: Two-phase validation approach (simulation → real data) for PHS system identification
2. **Novel Framework**: First application of learned PHS models with HNN/PySR to active ETF management
3. **Empirical Grounding**: Connects Economic Engineering theory to real financial market data
4. **Interpretable ML**: Demonstrates how physics-informed constraints can maintain interpretability in financial ML

### Practical Impact
1. **Validated Pipeline**: Robust system identification methodology applicable to other financial systems
2. **Cost Attribution**: Quantified understanding of ETF transaction cost structure and market impact
3. **Strategy Insights**: Data-driven revelation of ETF's true Economic Surplus function
4. **Risk Management**: Transparent decomposition of conservative vs. dissipative dynamics

### Deliverables
- **Phase 1**: Validated linear PHS identification pipeline with simulation studies
- **Phase 2**: Complete non-linear PHS model for ARKK with learned components
- **Software**: Open-source JAX/PySR implementation with reproducible pipeline
- **Thesis**: Comprehensive documentation bridging Economic Engineering theory and financial practice

## Discussion Points for Prof. Mendel

### 1. Theoretical Validation
- **Question:** How can we ensure the learned Hamiltonian $H(q,p)$ truly represents economic surplus rather than just a mathematical fit?
- **Proposed Approach:** Validate against known portfolio theory principles (mean-variance optimization, Kelly criterion)

### 2. Non-Linear Identifiability Assessment  
- **Question:** How can we assess identifiability for non-linear $H$, $R$, $g$ when formal proofs are intractable?
- **Proposed Approach:** Multi-pronged practical identifiability strategy: (1) PHS structural constraints as regularizers, (2) PySR's simplicity bias (Occam's razor), (3) Multiple random initializations testing convergence consistency, (4) Economic plausibility validation against market microstructure theory

### 3. Model Generalizability
- **Question:** How can we ensure the framework generalizes beyond ARKK to other active strategies?
- **Proposed Approach:** Test on additional ETFs (QQQ, SPY with options overlay) in validation phase

### 4. Economic Interpretability
- **Question:** What mechanisms ensure the discovered symbolic expressions are economically meaningful?
- **Proposed Approach:** Incorporate economic constraints in PySR search space, validate against known market microstructure

### 5. Computational Complexity
- **Question:** How do we handle the high-dimensional nature of portfolio holdings (typically 50-100 assets)?
- **Proposed Approach:** Leverage dimensionality reduction via PCA, or explore structural simplification by modeling the portfolio as a Walrasian agent coupled to a small number of underlying factor markets, consistent with the Economic Circuit Theory framework

## Risk Mitigation

### Technical Risks
- **HNN Training Instability:** Implement progressive training, careful initialization
- **PySR Convergence Issues:** Multiple random seeds, expanded operator library, economically-constrained operator sets
- **Data Quality Problems:** Robust preprocessing, outlier detection

### Methodological Risks
- **Error Propagation (HNN→PySR):** Mitigated through iterative refinement loop with convergence monitoring
- **Non-Linear Identifiability:** Addressed via multiple initialization consistency testing and simplicity bias
- **Economic Implausibility:** PySR search constraints, market microstructure validation, domain expert review

## Key Milestones

**Phase 1 Completion:**
- Validated linear PHS identification pipeline
- Understanding of parameter recovery capabilities and limitations
- Benchmark performance metrics for Phase 2 comparison

**Phase 2 Completion:**
- Complete non-linear PHS model for ARKK
- Symbolic expressions for economic frictions and input mappings
- Comprehensive validation and economic interpretation

## Request for Guidance

1. **Prioritization:** Which aspects should receive primary focus given time constraints?
2. **Collaboration:** Are there opportunities for collaboration with empirical finance researchers?
3. **Publication Strategy:** Which venues would be most appropriate for this interdisciplinary work?
4. **Extension Opportunities:** How might this work connect to broader Economic Engineering research program?

---

**Next Steps:**
1. Finalize data acquisition pipeline
2. Set up computational environment (JAX, PySR)
3. Begin preliminary HNN experiments
4. Schedule regular progress meetings (bi-weekly?)


### **Methodology: The Hamiltonian Decomposition Curriculum (HDC) Algorithm for PHS Discovery**

To solve the ill-posed inverse problem of discovering the latent structure of strategy—decomposing an agent's observed dynamics `ż_obs(t)` into its conservative (`H`), dissipative (`R`), and external (`g`) components—we introduce a novel estimation framework: the **Hamiltonian Decomposition Curriculum (HDC)**. This multi-stage approach is designed to overcome the challenges of joint optimization and ensure a robust and interpretable separation of dynamic effects.

The algorithm treats the agent's internal momentum `p(t)` as a latent state and the governing functions `θ = {H, R, g}` as unknown parameters. It follows the structure of an Expectation-Maximization (EM) algorithm, iteratively estimating the latent state (E-Step) and updating the parameters (M-Step). The core innovation lies in structuring the M-Step as a curriculum of progressively complex models, where parameters from earlier stages provide strong but adaptable priors for later stages.

#### **Notation:**

*   `z(t) = [q(t), p(t)]^T`: The full state vector, where `q(t)` are the observed holdings and `p(t)` is the latent momentum.
*   `ż_obs(t)`: The observed dynamics (changes in holdings) and a target for the unobserved momentum dynamics.
*   `u(t)`: The vector of observed external market signals (from the DFSV engine).
*   `θ_k = {H_k, R_k, g_k}`: The set of model parameters at iteration `k`.
*   `p_k(t)`: The estimated trajectory of the latent momentum state at iteration `k`.
*   `EKF(model, data)`: The state estimation function (e.g., Extended Kalman Filter/Smoother or UKF) that returns the posterior estimate of the latent state trajectory given a model and data.

---

#### **Algorithm Initialization**

1.  **Bootstrap Latent State (`p_0`):** Generate an initial estimate for the latent momentum trajectory, `p_0(t)`. This is a crucial first guess, derived from a simplified physical analogy (e.g., `p(t) = M(t) * q̇(t)`, where `M(t)` is a mass matrix estimated from rolling volatility).
2.  **Initialize Parameters (`θ_0`):** Set initial parameters to represent the simplest possible system:
    *   `H_0`: A randomly initialized Hamiltonian Neural Network (HNN).
    *   `R_0 = 0`: Assume a frictionless system.
    *   `g_0 = 0`: Assume a closed system with no external influence.

---

### **Phase 1: Conservative Core Curriculum**

**Objective:** Isolate and learn the agent's ideal, conservative strategy (`H`) and the corresponding latent momentum (`p`) in a frictionless, unforced world. This establishes a stable anchor for the entire model.

**Procedure:** Execute a dedicated EM loop until `H` and `p` converge.

**For `k` in `1...N_H` (H-EM Loop):**

*   **M-Step (Maximize for `H`):**
    Given the current latent state `p_{k-1}(t)`, train the HNN to find `H_k`. The HNN is trained to minimize the discrepancy between its implied conservative dynamics and the observed dynamics, using `p_{k-1}` as the target for the momentum part. The objective is to find:
    `H_k = argmin_H E[ || ż_obs(t) - J * ∇H(z(t)) ||² ]` where `z(t) = [q_obs(t), p_{k-1}(t)]^T`.

*   **E-Step (Estimate `p`):**
    Given the newly trained `H_k`, define the purely conservative state-space model:
    `Model_H_k: ż(t) = J * ∇H_k(z(t))`
    Run the state estimator to find the refined latent state trajectory:
    `p_k(t) = EKF(Model_H_k, q_obs(t))`

**Output of Phase 1:** Converged parameters `H_final_1` and latent state `p_final_1`.

---

### **Phase 2: Dissipative Dynamics Curriculum**

**Objective:** Introduce and learn the agent's internal frictions (`R`) while allowing the conservative core (`H`) to adapt. This accounts for real-world transaction costs and slippage.

**Procedure:** Execute a joint `(H, R)`-EM loop, using the results of Phase 1 as a powerful, regularized starting point.

**Initialization:**
*   Initialize `H` with the trained weights of `H_final_1`.
*   Initialize `p` with the trajectory `p_final_1`.
*   Initialize `R = 0`, `g = 0`.

**For `l` in `1...N_R` ((H, R)-EM Loop):**

*   **M-Step (Maximize for `H` and `R`):**
    1.  **Learn `R_l`:** Given the current state `p_{l-1}(t)` and Hamiltonian `H_{l-1}`, compute the dissipative residual: `r_R(t) = ż_obs(t) - (J * ∇H_{l-1}(z(t)))`. Use PySR to discover the dissipation function `R_l` that best explains this residual: `r_R(t) ≈ -R(z(t)) * ∇H_{l-1}(z(t))`.
    2.  **Fine-tune `H_l`:** Fine-tune the Hamiltonian `H` to get `H_l`, using a low learning rate. The training is regularized to prevent catastrophic forgetting of the conservative core discovered in Phase 1. The loss function is:
        `Loss = E[ || ż_obs(t) - (J - R_l) * ∇H(z(t)) ||² ] + λ_H * || H - H_final_1 ||²`
        The regularization term `λ_H` anchors the solution to the robust initial finding.

*   **E-Step (Estimate `p`):**
    Given the updated `H_l` and `R_l`, define the dissipative state-space model:
    `Model_R_l: ż(t) = (J - R_l) * ∇H_l(z(t))`
    Run the state estimator to find the refined latent state:
    `p_l(t) = EKF(Model_R_l, q_obs(t))`

**Output of Phase 2:** Converged parameters `H_final_2`, `R_final_2`, and latent state `p_final_2`.

---

### **Phase 3: External Coupling Curriculum**

**Objective:** Introduce and learn the agent's response to external market signals (`g`), allowing all internal parameters (`H`, `R`) to make final, fine-tuned adjustments.

**Procedure:** Execute a full `(H, R, g)`-EM loop, initialized with the results of Phase 2.

**Initialization:**
*   Initialize `H`, `R`, and `p` with `H_final_2`, `R_final_2`, and `p_final_2`.
*   Initialize `g = 0`.

**For `m` in `1...N_g` ((H, R, g)-EM Loop):**

*   **M-Step (Maximize for all parameters):**
    1.  **Learn `g_m`:** Given the current state `p_{m-1}(t)` and internal dynamics `{H_{m-1}, R_{m-1}}`, compute the final residual: `r_g(t) = ż_obs(t) - ((J - R_{m-1}) * ∇H_{m-1}(z(t)))`. Use PySR to discover the coupling function `g_m` that best explains this residual: `r_g(t) ≈ g_m(z(t)) * u(t)`.
    2.  **Fine-tune `R_m` and `H_m`:** Perform a final, joint fine-tuning of `R` and `H` using very low learning rates and regularization towards their previously converged states (`H_final_2`, `R_final_2`). This allows for small adaptations based on the full model, ensuring global self-consistency.

*   **E-Step (Estimate `p`):**
    Given the full set of updated parameters `θ_m = {H_m, R_m, g_m}`, define the complete state-space model:
    `Model_g_m: ż(t) = (J - R_m) * ∇H_m(z(t)) + g_m(z(t)) * u(t)`
    Run the state estimator to find the final refined latent state:
    `p_m(t) = EKF(Model_g_m, q_obs(t))`

**Final Output:** The fully discovered and self-consistent set of strategic laws, `{H_final, R_final, g_final}`, and the final estimated state trajectory, `p_final(t)`.
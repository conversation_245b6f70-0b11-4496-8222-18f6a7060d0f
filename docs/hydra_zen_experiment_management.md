# Hydra-Zen Experiment Management for Phynance

## Overview

This document outlines the recommended approach for experiment management in the Phynance project using `hydra_zen.launch`. This approach eliminates boilerplate code from your core scientific logic while maintaining all the powerful features of Hydra for configuration management and experiment tracking.

## Why hydra_zen.launch?

The standard `@hydra.main()` decorator approach creates tight coupling between your scientific code and the configuration framework. The `hydra_zen.launch` API solves this by:

1. **Zero Boilerplate**: Your core logic functions are pure Python with no framework dependencies
2. **Perfect Separation of Concerns**: Configuration handling is completely separate from scientific logic
3. **Enhanced Testability**: Core functions can be easily unit tested without Hydra runtime
4. **Automatic Provenance**: Maintains Hydra's critical experiment tracking and reproducibility features
5. **Full Hydra Power**: Retains CLI overrides, multirun capabilities, and sweep functionality

## Architecture Pattern

### Core Scientific Function (Framework-Agnostic)
```python
# Pure Python function - no decorators, no framework coupling
def run_phase1a_gateway(cfg: DictConfig) -> None:
    """
    Phase 1A Gateway validation logic.
    Receives configuration but is completely unaware of Hydra.
    """
    # Instantiate components from config
    phs_system = hydra.utils.instantiate(cfg.system.model)
    data_generator = hydra.utils.instantiate(cfg.data.generator)
    
    # Execute scientific logic
    synthetic_data = data_generator.generate()
    results = phs_system.validate(synthetic_data)
    
    # Save results (Hydra automatically provides working directory)
    results.save("validation_results.pkl")
    
    return results
```

### Execution Script (Hydra Integration)
```python
# scripts/run_phase1a_gateway.py
from hydra_zen import launch
from config.phase1a_configs import P1A_AppConfig
from phynance.core.validation import run_phase1a_gateway

if __name__ == "__main__":
    launch(
        P1A_AppConfig,           # Configuration schema
        run_phase1a_gateway,     # Pure scientific function
        version_base=None,
        multirun=True           # Enable experiment sweeps
    )
```

## Project Structure Integration

```
phynance/
├── config/
│   ├── phase1a_configs.py      # hydra-zen config definitions
│   ├── phase1b_configs.py
│   └── phase2_configs.py
├── scripts/
│   ├── run_phase1a_gateway.py  # Launch scripts using hydra_zen.launch
│   ├── run_phase1b_nonlinear.py
│   └── run_phase2_discovery.py
├── src/phynance/
│   ├── core/
│   │   └── validation.py       # Pure scientific functions
│   ├── synthetic/
│   │   └── experiments.py      # Core experiment logic
│   └── real_data/
│       └── discovery.py        # Discovery pipeline logic
└── outputs/                    # Hydra-managed experiment outputs
    └── YYYY-MM-DD/
        └── HH-MM-SS/          # Timestamped experiment runs
            ├── .hydra/        # Configuration provenance
            ├── logs/          # Automatic logging
            └── results/       # Your experiment outputs
```

## Configuration Management

### Phase 1A Example Configuration
```python
# config/phase1a_configs.py
from hydra_zen import builds, make_config
from phynance.synthetic.linear_system import LinearCoupledPHS
from phynance.synthetic.data_generation import SyntheticDataGenerator

# Component configurations
LinearPHSConfig = builds(
    LinearCoupledPHS,
    m_agent=1.2,
    k_agent=0.8,
    m_market=2.0,
    k_market=1.5,
    populate_full_signature=True
)

DataGenConfig = builds(
    SyntheticDataGenerator,
    n_samples=1000,
    noise_level=0.01,
    random_seed=42,
    populate_full_signature=True
)

# Application configuration
P1A_AppConfig = make_config(
    system=make_config(model=LinearPHSConfig),
    data=make_config(generator=DataGenConfig),
    experiment=make_config(
        name="phase1a_gateway",
        description="Linear coupled PHS identifiability validation"
    )
)
```

## Experiment Execution Patterns

### Single Experiment Run
```bash
python scripts/run_phase1a_gateway.py
```

### Parameter Sweeps (Multirun)
```bash
# Sweep over agent mass values
python scripts/run_phase1a_gateway.py --multirun system.model.m_agent=1.0,1.2,1.5,2.0

# Sweep over noise levels and system parameters
python scripts/run_phase1a_gateway.py --multirun \
    data.generator.noise_level=0.001,0.01,0.1 \
    system.model.k_agent=0.5,0.8,1.0
```

### Configuration Overrides
```bash
# Override specific parameters
python scripts/run_phase1a_gateway.py \
    system.model.m_agent=2.0 \
    data.generator.n_samples=2000 \
    experiment.name="high_mass_validation"
```

## Testing Integration

The framework-agnostic design enables easy unit testing:

```python
# tests/test_phase1a.py
import pytest
from omegaconf import DictConfig
from phynance.core.validation import run_phase1a_gateway

def test_phase1a_basic_functionality():
    """Test Phase 1A logic with minimal config."""
    # Create simple test configuration
    test_cfg = DictConfig({
        "system": {"model": {"_target_": "...", "m_agent": 1.0}},
        "data": {"generator": {"_target_": "...", "n_samples": 100}}
    })
    
    # Test core function directly
    results = run_phase1a_gateway(test_cfg)
    
    # Assert expected behavior
    assert results is not None
    assert results.validation_passed
```

## Experiment Tracking and Reproducibility

### Automatic Provenance
Every experiment run automatically saves:
- Complete configuration used (`.hydra/config.yaml`)
- Hydra overrides applied (`.hydra/overrides.yaml`)
- Execution logs with timestamps
- Working directory for outputs

### Experiment Organization
```
outputs/
├── 2025-01-15/           # Date-based organization
│   ├── 09-30-45/        # Time-based sub-directories
│   │   ├── .hydra/      # Configuration provenance
│   │   ├── logs/        # Execution logs
│   │   └── results.pkl  # Your experiment outputs
│   └── 14-22-10/        # Another experiment run
└── multirun/            # Sweep experiment results
    └── 2025-01-15_16-45-30/
        ├── 0/           # First parameter combination
        ├── 1/           # Second parameter combination
        └── ...
```

## Advanced Features

### Custom Resolvers for Dynamic Configuration
```python
# config/resolvers.py
from hydra_zen import make_custom_builds_fn
from omegaconf import OmegaConf

# Register custom resolver for dynamic paths
OmegaConf.register_new_resolver(
    "data_path", 
    lambda dataset: f"/data/phynance/{dataset}"
)

# Use in configuration
DataConfig = make_config(
    path="${data_path:arkk_holdings}",  # Resolves to /data/phynance/arkk_holdings
    format="parquet"
)
```

### Experiment Composition
```python
# Compose configurations for different phases
Phase1Config = make_config(
    base=P1A_AppConfig,
    validation=make_config(
        metrics=["mse", "identifiability_score"],
        thresholds=make_config(mse_max=1e-4)
    )
)
```

## Integration with Phase Structure

### Phase 1A: Linear System Validation
- **Function**: `run_phase1a_gateway(cfg: DictConfig)`
- **Config**: `P1A_AppConfig`
- **Launch**: `launch(P1A_AppConfig, run_phase1a_gateway)`

### Phase 1B: Non-Linear System Validation  
- **Function**: `run_phase1b_nonlinear(cfg: DictConfig)`
- **Config**: `P1B_AppConfig`
- **Launch**: `launch(P1B_AppConfig, run_phase1b_nonlinear)`

### Phase 2: Real Data Discovery
- **Function**: `run_phase2_discovery(cfg: DictConfig)`
- **Config**: `P2_AppConfig`
- **Launch**: `launch(P2_AppConfig, run_phase2_discovery)`

## Benefits for Thesis Research

1. **Reproducibility**: Every experiment is automatically documented with full provenance
2. **Scalability**: Easy to run parameter sweeps and ablation studies
3. **Modularity**: Core scientific code remains clean and testable
4. **Collaboration**: Configurations are version-controlled and shareable
5. **Debugging**: Clear separation between configuration and logic issues

## Migration Strategy

1. **Start with Phase 1A**: Implement the `hydra_zen.launch` pattern for the gateway validation
2. **Extract Core Logic**: Move scientific functions to pure Python (no decorators)
3. **Create Configurations**: Define hydra-zen configs for all components
4. **Test Integration**: Ensure experiment tracking and multirun work correctly
5. **Scale to Other Phases**: Apply the same pattern to Phase 1B and Phase 2

This approach will provide a robust foundation for managing the complex experimental pipeline required for your thesis research while maintaining clean, testable, and reproducible code.
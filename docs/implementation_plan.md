# **Implementation Plan: The Structure of Strategy - A First-Principles Approach to Discovering the Dynamics of Active ETFs**

**Student:** <PERSON><PERSON><PERSON>  
**Supervisor:** Prof<PERSON>  
**Date:** July 2025

## Overview

This implementation plan supports the hybrid system identification pipeline designed to discover interpretable, first-principles laws governing an active ETF's interaction with its market environment. The approach combines Economic Engineering principles with modern machine learning techniques to solve the "Residual Diagnostics Puzzle" identified in prior work.

The implementation unfolds in two distinct phases:
1. **Phase 1 - Pipeline Development & Validation:** Complete validation of methodology on synthetic data
2. **Phase 2 - Application & Discovery:** Validated pipeline applied to real ARKK data

## Technology Stack

- **Core:** Python/JAX for system identification and automatic differentiation
- **Market Engine:** DFSV-BIF (validated framework from previous thesis)  
- **Agent Engine:** Port-Hamiltonian Systems with Hamiltonian Neural Networks
- **Discovery Engine:** PySR for symbolic regression of interpretable laws
- **Additional:** JAX/Flax, Diffrax, Optax, Optimistix/Equinox, SymPy

---

## **Phase 1: Pipeline Development & Validation on Synthetic Data**

**Timeline:** July - September 2025  
**Objective:** Prove methodology can recover latent states and symbolic laws in controlled environment

### **Sub-Phase 1A: Grey-Box PEM for Linear Coupled Port-Hamiltonian System**

**Duration:** 3 weeks  
**Critical Go/No-Go Checkpoint:** Establishes structural identifiability of core problem

#### **Week 1: Theoretical Foundation & System Design**
- **Task 1.1:** Formalize coupled agent-market PHS system
  - Define agent state: $z_{agent} = [q_{agent}, p_{agent}]^T$
  - Define market state: $z_{market} = [q_{market}, p_{market}]^T$  
  - Establish coupled dynamics: $\frac{dz}{dt} = (J_{total} - R_{total})\nabla H_{total}(z) + g_{total}u_{ext}(t)$
- **Task 1.2:** Define parameter set for identification
  - Conservative parameters: $\{m_{agent}, k_{agent}, q_{ref}, m_{market}, k_{market}\}$
  - Dissipative parameters: $R_{total}$ structure and coupling terms
  - Input coupling: $g_{total}$ parameters

#### **Week 2: Implementation & Simulation**  
- **Task 1.3:** Build JAX-based coupled PHS simulator
  - Modular architecture supporting both agent and market subsystems
  - Integration with Optimistix/Equinox ODE solvers
- **Task 1.4:** Generate synthetic training data
  - Known ground truth $\theta_{true}$ parameters
  - Realistic noise levels on partial observations $y = [q_{agent}, p_{market}]^T$

#### **Week 3: Identification & Validation**
- **Task 1.5:** Implement Prediction Error Method (PEM)
  - Gradient-based optimization with JAX autodiff
  - Cost function design for partial observations
- **Task 1.6:** Formal identifiability analysis
  - Transfer function analysis of coupled LTI system
  - **Critical Decision Point:** If parameters unidentifiable, redesign observation scheme
- **Task 1.7:** Benchmark performance metrics for Phase 2

### **Sub-Phase 1B: Full Iterative Pipeline Validation on Non-Linear System**

**Duration:** 4 weeks  
**Objective:** Validate complete HNN-PySR-EKF iterative loop

#### **Weeks 4-5: Non-Linear System Design & Data Generation**
- **Task 1.8:** Design non-linear synthetic agent PHS
  - Non-linear Hamiltonian: $H = \frac{1}{2}k_1 q^2 + \frac{1}{2m}p^2 + k_2 q^3 + k_3 qp^2$
  - Symbolic friction laws: $R = r_1 |p| + r_2 p^2$  
  - Coupling functions: $g = g_0 + g_1 q$
- **Task 1.9:** Generate synthetic datasets
  - Multiple initial conditions and random seeds
  - Observable data: $q_{obs}(t) = q_{true}(t) + \epsilon_q$
  - Latent state problem: $p_{true}(t)$ unobservable

#### **Weeks 6-7: Iterative Discovery Implementation**
- **Task 1.10:** Implement bootstrap strategies
  - Velocity-based: $p_0 = m \cdot \dot{q}_{obs}$
  - Spread-based: $p_0 = p_{market} + \text{estimated spread}$
  - Random walk: $p_0$ as smoothed process
- **Task 1.11:** Build EM discovery loop
  - **M-Step:** HNN training with conservation constraints
  - **E-Step:** EKF state update with automatic differentiation  
  - **M-Step:** PySR symbolic discovery of $R(q,p)$ and $g(q,p)$
- **Task 1.12:** Integration with JAX/Flax and Diffrax

#### **Week 8: Critical Recovery Analysis**
- **Task 1.13:** Validate discovery performance
  - Hamiltonian recovery: $H_{discovered}$ vs $H_{true}$
  - Symbolic law recovery: PySR accuracy on functional forms
  - State trajectory recovery: $||p_{final} - p_{true}||$
  - Bootstrap independence: consistency across initialization methods
- **Task 1.14:** Robustness assessment
  - Sensitivity to noise levels and data length
  - **Final Validation:** If convergence fragile, improve algorithm before Phase 2

---

## **Phase 2: Application to Real ETF Data & Discovery**

**Timeline:** October 2025 - January 2026  
**Objective:** Apply validated pipeline to real ARKK data for discovery of unknown laws

### **Step 1: Market Environment Characterization**

**Duration:** 2 weeks

- **Task 2.1:** Data acquisition pipeline
  - ARKK holdings data (2014-2025)
  - Market prices, alpha signals, VIX, fund flows
  - Data cleaning and timestamp alignment
- **Task 2.2:** DFSV market state extraction  
  - Apply validated BIF methodology from previous thesis
  - Extract stationary factor returns $f_t$ and log-volatilities $h_t$
  - Construct market state: $z_{market}(t) = [f_t^T, h_t^T]^T$
- **Task 2.3:** External input construction
  - Market factors and volatilities as $u(t)$
  - Fund-specific inputs: flows, strategy changes

### **Step 2: Data Preprocessing & Bootstrap**

**Duration:** 2 weeks

- **Task 2.4:** Advanced preprocessing pipeline
  - Handle missing values and microstructure noise
  - Smoothing splines for continuous analytical functions
  - Analytical derivative calculation: $\dot{z}(t)$ from fitted splines
- **Task 2.5:** Economic validation
  - Trading volume consistency checks
  - Market microstructure plausibility assessment
- **Task 2.6:** Bootstrap initial state
  - Compute $p_0 = m \cdot \dot{q}_{obs}$ using inverse rolling volatility as inertia proxy

### **Step 3: Iterative Discovery Loop Implementation**

**Duration:** 6 weeks

#### **Weeks 3-4: Initial Setup & First Iteration**
- **Task 2.7:** Initialize EM algorithm
  - Bootstrap momentum guess $p_0$
  - Conservative assumption: $R_0=0$, $g_0=0$
  - Initial HNN training: $H_0$ on state $z_0 = [q_{obs}, p_0]$

#### **Weeks 5-6: Core EM Loop Implementation**
- **Task 2.8:** M-Step: Non-conservative physics update
  - Calculate residual dynamics: $\text{residual}_k = \frac{dz}{dt} - J \cdot \nabla H_{k-1}$
  - PySR symbolic discovery: updated $R_k$ and $g_k$
- **Task 2.9:** E-Step: Latent state update  
  - EKF implementation with full PHS dynamics as transition model
  - Observable holdings $q_{obs}$ as measurement model
  - Output: filtered momentum trajectory $p_k$

#### **Weeks 7-8: Conservative Physics & Convergence**
- **Task 2.10:** M-Step: Conservative physics update
  - Calculate cleaned dynamics: subtract non-conservative influence
  - Retrain HNN: $H_k$ on updated state $z_k = [q_{obs}, p_k]$
- **Task 2.11:** Convergence monitoring
  - Symbolic law stability across iterations
  - L2-norm change in latent state trajectory < 1e-4
  - Maximum 20 iterations with early stopping

### **Step 4: Final Output & Economic Interpretation**

**Duration:** 2 weeks

- **Task 2.12:** Discovery analysis
  - Learned Hamiltonian $H_{agent}$ representing ARKK's "Economic Surplus"
  - Symbolic equations for $R_{agent}$ (frictions) and $g$ (market interactions)
  - Final trajectory of latent internal price $p_{agent}$
- **Task 2.13:** Economic insight extraction
  - Hamiltonian landscape interpretation
  - Friction structure and cost attribution
  - Strategy insights and regime identification
  - Risk management applications

---

## **Phase 3: Results Analysis & Validation**

**Timeline:** February - March 2026

### **Analysis & Validation Tasks**
- **Task 3.1:** Model performance evaluation
  - In-sample goodness-of-fit for state trajectories
  - Out-of-sample forecasting ability (short horizons)
  - Reproduction of ETF stylized facts
- **Task 3.2:** Comparative analysis
  - Performance vs. linear PHS benchmark
  - Comparison with traditional financial models
- **Task 3.3:** Economic interpretation deep-dive
  - Performance attribution to $H$, $R$, $gu$ components
  - Risk factor sensitivities from $g$
  - Scenario analysis and cost structure insights

---

## **Phase 4: Documentation & Thesis Writing**

**Timeline:** April - June 2026

### **Core Documentation**
- **Task 4.1:** Methodology documentation
  - Phase 1 validation results and pipeline description
  - Phase 2 discovery results and economic interpretation
- **Task 4.2:** Software deliverables
  - Open-source JAX/PySR implementation
  - Reproducible pipeline with example datasets
- **Task 4.3:** Thesis completion
  - Comprehensive documentation bridging theory and practice
  - Results suitable for academic publication

---

## Risk Mitigation & Feasibility

### **Technical Risk Management**
- **EM Convergence Issues:** Damped updates, modular testing, stability monitoring
- **HNN Training Problems:** Progressive training, conservation property monitoring  
- **PySR Discovery Failures:** Multiple seeds, expanded operators, economic constraints
- **Data Quality Issues:** Robust preprocessing, outlier detection, derivative validation

### **Computational Complexity Management**
- **High-Dimensional Holdings:** PCA dimensionality reduction, Walrasian agent framework
- **EM Loop Intensity:** Clear convergence criteria, estimated timeline feasibility
- **Processing Requirements:** DFSV preprocessing (1 day), EM iterations (minutes to hours)

### **Critical Success Factors**
- **Phase 1 Gateway:** Formal proof of parameter identifiability from partial observations
- **Bootstrap Robustness:** Consistent convergence across initialization methods
- **Economic Plausibility:** Discovered laws must align with financial theory
- **Market Completeness:** DFSV factors must capture sufficient market dynamics

## Expected Deliverables

1. **Validated linear PHS identification pipeline** with formal identifiability proofs
2. **Complete non-linear PHS model for ARKK** with learned Hamiltonian and symbolic laws  
3. **Open-source implementation** in JAX/PySR with reproducible examples
4. **Comprehensive thesis** documenting methodology and economic insights
5. **Academic publication** bridging Economic Engineering theory and financial practice

This implementation plan provides a structured pathway from theoretical framework to practical discovery, with clear milestones and risk mitigation strategies throughout the development process.
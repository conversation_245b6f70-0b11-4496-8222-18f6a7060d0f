
# **The Structure of Strategy: A First-Principles Approach to Discovering the Dynamics of Active ETFs**

**Student:** <PERSON><PERSON><PERSON>
**Supervisor:** Prof. <PERSON> (placeholder)
**Date:** July 2025

## Executive Summary

This thesis develops and validates a novel, hybrid system identification pipeline designed to discover the interpretable, first-principles laws governing an active ETF's interaction with its market environment. This research is directly motivated by the "Residual Diagnostics Puzzle" identified in my prior Master's thesis, which demonstrated that even state-of-the-art DFSV models fail due to misspecification of agent-specific dynamics.

To solve this, the proposed research unfolds in two distinct phases:
1.  **Phase 1 - Pipeline Development & Validation:** A complete, end-to-end validation of the methodology on synthetic data. This phase uses a two-stage approach. First, a **grey-box Prediction Error Method (PEM)** is applied to a linear interconnected toy model to formally prove structural identifiability from partial observations. Second, the full iterative discovery engine is validated on a non-linear synthetic system, proving its ability to recover known Hamiltonians and symbolic interaction laws.
2.  **Phase 2 - Application & Discovery:** The validated pipeline is applied to real-world data for the ARK Innovation ETF (ARKK). The workflow begins by characterizing the market environment using a DFSV-BIF model. The core iterative discovery loop is then deployed to simultaneously infer the agent's hidden internal state (its "economic momentum") and discover the symbolic physics of its internal frictions and external market coupling.

This work bridges the gap between statistical econometrics and first-principles Economic Engineering, aiming not just to fit data, but to discover the transparent, self-consistent structural laws that govern complex financial agent behavior.

## Core Research Motivation

### The Problem: The "Residual Diagnostics Puzzle"
My previous Master's thesis demonstrated that a sophisticated DFSV model estimated with a Bellman Information Filter (BIF) could successfully capture aggregate market dynamics. However, it also revealed a critical insight: the model failed standard diagnostic tests for conditional heteroskedasticity at the individual asset level. This proves that significant, dynamic, agent-specific behavior remains in the residuals after accounting for common market factors. The core problem is therefore **model misspecification**; even state-of-the-art factor models are structurally incomplete.

### The Challenge Chain: From Misspecification to Discovery
Solving this puzzle requires overcoming a chain of three fundamental challenges:
1.  **The Latent State Problem:** A first-principles Port-Hamiltonian System (PHS) model requires knowledge of the agent's internal state, specifically its "economic momentum" or reservation price (`p_agent`), which is unobservable.
2.  **The Endogeneity Trap:** Using the observable market price as a simple proxy for this internal price is methodologically flawed, as it makes it impossible to separate the agent's internal strategy from its market reactions.
3.  **The "Chicken-and-Egg" Problem:** We cannot know the physical laws (`H, R, g`) without knowing the state trajectory (`p_agent`), but we cannot robustly infer the state trajectory without knowing the physical laws that govern it.

### The Opportunity: An Iterative, Self-Consistent Solution
The proposed solution is a hybrid, iterative discovery pipeline that solves these challenges sequentially. It combines a phenomenological market model with a first-principles agent model, using an Expectation-Maximization (EM)-like algorithm to break the "chicken-and-egg" deadlock and converge to a self-consistent solution.

## Theoretical Foundation

The core of this thesis is a strategic separation of concerns, grounded in the principles of Economic Engineering and Port-Hamiltonian Systems.

1.  **The Market Model (Phenomenological):** The market is treated as a complex, external environment. Its state `z_market` is characterized empirically using a high-fidelity statistical model (DFSV).
2.  **The Agent Model (First-Principles):** The active ETF is modeled as a Port-Hamiltonian System. Its dynamics are governed by its internal state `z_agent = [q_aᵀ, p_aᵀ]ᵀ` (holdings and internal reservation prices). The governing equation for the agent is:
    $$ \frac{dz_{agent}}{dt} = (J - R_{agent}(z_{agent}))\nabla H_{agent}(z_{agent}) + g(z_{agent}, z_{market})u(t) $$
    where `H` is the agent's internal "Economic Surplus" (a conservative Hamiltonian), `R` represents internal dissipative frictions, and `g` is the interconnection term that defines the agent's coupling to the external market `z_market`. The central scientific goal is to discover the functional forms of `H`, `R`, and `g`.

## Framework & Technology Stack Justification

This research integrates three advanced methodologies into a single, cohesive pipeline, with Python/JAX as the unifying implementation language.

*   **DFSV-BIF (The Market Engine):** Leveraging the validated framework from my first thesis provides a robust, computationally efficient, and reliable method for extracting the latent market state.
*   **PHS/HNN (The Agent Engine):** Port-Hamiltonian Systems provide the ideal theoretical language for the agent model. A Hamiltonian Neural Network (HNN) is the perfect tool to learn the potentially complex, non-linear form of the agent's internal objective function `H_agent` from data.
*   **PySR (The Discovery Engine):** After modeling the conservative dynamics with the HNN, the residual non-conservative and coupling dynamics can be isolated. PySR's ability to find simple, interpretable symbolic expressions from data makes it the ideal tool to discover the "laws" governing `R_agent` and `g`.

## Methodology: A Two-Phase Approach

The research is structured into two primary phases: a rigorous validation phase on synthetic data, followed by an application and discovery phase on real-world ETF data.

### **Phase 1: Pipeline Development & Validation on Synthetic Data**
**Objective:** To prove, in a controlled environment where the ground truth is known, that the proposed identification pipeline is sound, its components work as intended, and it can successfully recover latent states and symbolic laws. This phase proceeds in two distinct stages of increasing complexity.

*   **Sub-Phase 1A: Grey-Box PEM for a Linear Interconnected Toy Model**
    *   **Objective:** To formally establish the structural identifiability of the core problem—a coupled agent-market PHS system—from partial and noisy observations, using classical system identification theory.
    *   **Theoretical Foundation:** Define a 4-state linear interconnected PHS model (`z = [q_a, p_a, q_m, p_m]ᵀ`) with known Hamiltonians and known linear friction and coupling parameters (`θ_true`).
    *   **Implementation & Validation:**
        1.  **Simulate Data:** Generate trajectories from this LTI system, observing only two states (`q_a`, `p_m`) and adding Gaussian noise.
        2.  **Implement PEM:** Develop a grey-box Prediction Error Method (PEM) engine in Python/JAX. The "grey-box" structure will assume the known PHS form but treat the physical parameters (`k_a`, `m_a`, `R_coupling`, etc.) as unknown.
        3.  **Recovery Analysis:** Quantify the engine's ability to accurately recover the *true* parameters (`θ_identified` vs. `θ_true`) from the noisy data.
    *   **Critical Outcome:** A formal, quantitative validation that the physical parameters of an interconnected PHS system are identifiable in principle, providing a solid theoretical foundation before engaging with more complex ML tools.

*   **Sub-Phase 1B: Full Iterative Pipeline Test on a Non-Linear Symbolic System**
    *   **Objective:** To validate the complete HNN-PySR iterative discovery loop's ability to learn a non-linear Hamiltonian and discover a known symbolic interaction law, solving the full latent variable problem.
    *   **Theoretical Foundation:** Define a synthetic system where `H_agent` is a known non-linear function and the coupling `g` is a known symbolic law.
    *   **Implementation & Validation:**
        1.  **Simulate Data:** Generate the "true" latent state trajectory `z_agent_true` from the known physics.
        2.  **Bootstrap Guess & Robustness Check:** Create the initial momentum guess `p_0` using the `p=mv` method. To ensure the robustness of the discovery, the EM loop's convergence will be tested against multiple, distinct bootstrap initializations for the latent state (e.g., a smooth random walk, a market-spread-based guess).
        3.  **Execute EM Loop:** Run the iterative discovery engine.
        4.  **Critical Recovery Analysis:** After the loop converges, verify that the discovered laws (`H, R, g`) and the final inferred state (`p_final`) match their known ground truths across the different initializations.
    *   **Critical Outcome:** A definitive, end-to-end validation of the full discovery pipeline, proving its ability to separate dynamics and discover their true functional forms robustly.

### **Phase 2: Application to Real ETF Data & Discovery**
**Objective:** To apply the validated pipeline to real data for the ARKK ETF to discover the unknown, interpretable laws governing its market interaction.

*   **Step 1: Characterize the Environment (The Market Engine)**
    *   **Action:** In a crucial preprocessing step, estimate a high-dimensional DFSV model on a broad market universe using the validated BIF methodology. Extract the latent, stationary factor returns (`f_t`) and their log-volatilities (`h_t`).
    *   **Output:** The final market state vector is `z_market(t) = [f_t(t)ᵀ, h_t(t)ᵀ]ᵀ`. This pre-computed, stationary time series serves as a known, exogenous input, resolving the stationarity concerns of earlier plans.

*   **Step 2: Bootstrap the Agent's State (The Initial Guess)**
    *   **Action:** Acquire ARKK's holdings data (`q_obs`) and compute its trading velocity (`q̇_obs`). Proxy inertia (`m`) using the inverse of rolling asset volatility. Calculate the initial, non-endogenous guess for the agent's internal momentum: `p_0 = m * q̇_obs`.

*   **Step 3: The Iterative Discovery Loop (The EM Engine)**
    *   **Action:** Execute the core EM discovery loop using the real data (`z_market`, `q_obs`, `p_0`).
        *   **(M-Step) Model Update:** Given the current state estimate `p_k`, use an HNN to learn the conservative Hamiltonian `H_{k+1}`. Use PySR on the residuals to discover the symbolic laws for friction `R_{k+1}` and market coupling `g_{k+1}`.
        *   **(E-Step) State Update:** Given the newly discovered physical laws (`H, R, g`), use a Bellman Filter to produce a new, dynamically consistent, and refined estimate of the internal momentum trajectory, `p_{k+1}`.
        *   **Iterate:** Repeat the M-Step and E-Step until convergence.
    *   **Convergence Criteria:** The EM loop will be considered converged when a set of joint criteria are met: the core symbolic laws discovered by PySR remain stable across several iterations, the L2-norm of the change in the latent state trajectory (`||p_{k+1} - p_k||`) falls below a pre-defined threshold (e.g., 1e-4), and a maximum number of iterations (e.g., 20) is not exceeded.

*   **Step 4: The Final Output & Interpretation**
    *   **The Discovery:** The pipeline's final output is a set of interpretable, first-principles results:
        1.  A learned Hamiltonian `H_agent` representing ARKK's "Economic Surplus" function.
        2.  A set of symbolic equations for `R_agent` and `g` that govern its frictions and market interactions.
        3.  The final, inferred trajectory of its latent internal price, `p_agent`.
    *   **Interpretation:** Perform a deep economic analysis of these results, explaining what the shape of the Hamiltonian implies about ARKK's strategy and what the discovered laws reveal about its trading costs and response to market forces.

## Expected Outcomes & Contributions

*   **Methodological:** A novel and fully validated, iterative pipeline for discovering agent-based economic models from data. This constitutes a new instrument for financial system identification.
*   **Empirical:** The first-ever discovery of a symbolic, data-driven law for an active ETF's interaction with its market, providing a concrete, structural solution to the misspecification puzzle identified in prior research.
*   **Theoretical:** A powerful, practical demonstration of how Economic Engineering principles can be combined with modern ML to create interpretable, first-principles models of complex economic systems.
*   **Deliverables:** A complete, high-quality thesis document detailing the theory, validation, and findings; a robust, open-source Python implementation of the entire reproducible pipeline; and a compelling narrative of scientific discovery.

## Discussion Points for Supervisor

1.  **Convergence of the EM Loop:** How can we formally assess the convergence of the iterative procedure? What criteria should be used to stop the loop (e.g., stability in the symbolic laws, minimal change in the `p_agent` trajectory)?
2.  **Quality of the Bootstrap:** How sensitive is the final converged solution to the quality of the initial `p_0` guess? We must test whether different plausible bootstrapping methods lead to the same final laws.
3.  **Economic Constraints on Discovery:** How can we best constrain the PySR search space in Phase 2 to ensure the discovered laws are not only mathematically simple but also economically plausible? Should we pre-define a library of operators based on economic theory (e.g., spreads, squares of velocities)?
4.  **Uniqueness and Robustness of Symbolic Laws:** How can we ensure the laws discovered by PySR are unique and not just one of several good fits? This involves exploring the Pareto front of accuracy vs. complexity that PySR produces and testing for robustness across different data subsets (e.g., bull vs. bear markets).

## Risk Mitigation & Feasibility

*   **Technical Risk: EM Loop Fails to Converge.**
    *   **Mitigation:** This is a primary risk. It will be mitigated by using "damped" updates in the E-Step (i.e., `p_{k+1} = (1-α)p_k + α*p_new`) to ensure stability. The modular design allows for robust testing of each component (M-step and E-step) independently.
*   **Methodological Risk: Model Identifiability.**
    *   **Mitigation:** This is the purpose of Sub-Phase 1A. By proving that the parameters of a simplified linear system are identifiable with a classical PEM approach, we build strong confidence that the more complex non-linear system is also well-posed.
*   **Interpretation Risk: Discovered Laws are Unphysical or Uninterpretable.**
    *   **Mitigation:** This is a core scientific risk. It is mitigated by a) constraining the PySR operator set based on economic first principles and b) making the economic interpretation of the final discovered laws a central part of the analysis, subject to critical review against established theory. The goal is not just a fit, but an explanation.
*   **Computational Feasibility:** The primary computational load lies in the EM loop. Based on prior work, the one-time DFSV estimation is feasible within a day. Each EM iteration, involving HNN training over a high-dimensional state space and a complex PySR discovery task, is estimated to be on the order of minutes to hours. The full discovery phase is computationally intensive but is assessed to be feasible within the dedicated timeline for a Master's thesis, given the modular design and clear convergence criteria.
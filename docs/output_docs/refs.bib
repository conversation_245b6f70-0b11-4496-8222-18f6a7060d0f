@paper{cranmerInterpretableMachineLearning2023,
  title = {Interpretable {{Machine Learning}} for {{Science}} with {{Py<PERSON>}} and {{SymbolicRegression}}.Jl},
  author = {<PERSON><PERSON><PERSON>, Miles},
  date = {2023-05-05},
  eprint = {2305.01582},
  eprinttype = {arXiv},
  eprintclass = {astro-ph},
  doi = {10.48550/arXiv.2305.01582},
  url = {http://arxiv.org/abs/2305.01582},
  urldate = {2025-05-06},
  abstract = {PySR is an open-source library for practical symbolic regression, a type of machine learning which aims to discover human-interpretable symbolic models. PySR was developed to democratize and popularize symbolic regression for the sciences, and is built on a high-performance distributed back-end, a flexible search algorithm, and interfaces with several deep learning packages.},
  pubstate = {prepublished},
  keywords = {Astrophysics - Instrumentation and Methods for Astrophysics,Computer Science - Machine Learning,Computer Science - Neural and Evolutionary Computing,Computer Science - Symbolic Computation,Physics - Data Analysis Statistics and Probability}
}

@paper{greydanusHamiltonianNeuralNetworks2019a,
  title = {Hamiltonian {{Neural Networks}}},
  author = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>ski, <PERSON>},
  date = {2019-09-05},
  eprint = {1906.01563},
  eprinttype = {arXiv},
  eprintclass = {cs},
  doi = {10.48550/arXiv.1906.01563},
  url = {http://arxiv.org/abs/1906.01563},
  urldate = {2025-05-06},
  abstract = {Even though neural networks enjoy widespread use, they still struggle to learn the basic laws of physics. How might we endow them with better inductive biases? In this paper, we draw inspiration from Hamiltonian mechanics to train models that learn and respect exact conservation laws in an unsupervised manner.},
  pubstate = {prepublished},
  keywords = {Computer Science - Neural and Evolutionary Computing}
}

@paper{mendelNewtonianMechanicsDemand2023,
  type = {Working paper},
  title = {The {{Newtonian Mechanics}} of {{Demand}}},
  author = {Mendel, Max},
  date = {2023-10},
  institution = {arXiv.org},
  url = {https://EconPapers.repec.org/RePEc:arx:papers:2310.17423},
  urldate = {2025-05-06},
  abstract = {Economic engineering is a new field wherein economic systems are modelled in the same manner as traditional mechanical and electrical engineering systems. In this paper, we use Newton's theory of motion as the basis for the theory of demand; thereby establishing a theoretical foundation for economic engineering.}
}

@misc{ARKKHoldings,
  title        = {ARKK Daily ETF Holdings},
  author       = {{ARK Investment Management}},
  year         = {2025},
  howpublished = {\url{https://ark-funds.com/funds/arkk/holdings}},
  note         = {CSV download, accessed 2025-05-07}
}

@misc{YahooFinancePrices,
  title        = {Historical Daily Prices for ARKK Constituents},
  author       = {{Yahoo Finance}},
  year         = {2025},
  howpublished = {\url{https://finance.yahoo.com}},
  note         = {Ticker‐level close prices, accessed 2025-05-07}
}

@misc{CBOE-VIX,
  title        = {CBOE Volatility Index (VIX) Daily History},
  author       = {{Chicago Board Options Exchange}},
  year         = {2025},
  howpublished = {\url{https://www.cboe.com}},
  note         = {Accessed 2025-05-07}
}

@misc{ARKKFlows,
  title        = {ARKK Shares Outstanding and Net Fund Flows},
  author       = {{ARK Investment Management}},
  year         = {2025},
  howpublished = {\url{https://ark-funds.com/funds/arkk}},
  note         = {Derived daily flow series, accessed 2025-05-07}
}

@article{vanderSchaftPortHamiltonianSystems2000,
  title = {Port-{{Hamiltonian Systems Theory}}: {{An Introductory Overview}}},
  author = {van der Schaft, Arjan and Maschke, Bernhard},
  date = {2000},
  journaltitle = {Foundations and Trends in Systems and Control},
  volume = {1},
  number = {2-3},
  pages = {173--378},
  doi = {10.1561/2600000002},
  abstract = {Port-Hamiltonian systems theory provides a framework for the geometric description of network models of physical systems. It turns out that port-Hamiltonian systems are very well suited for analysis, simulation and control.}
}

@book{marsdenIntroductionMechanicsSymmetry2013,
  title = {Introduction to {{Mechanics}} and {{Symmetry}}: {{A Basic Exposition}} of {{Classical Mechanical Systems}}},
  author = {Marsden, Jerrold E. and Ratiu, Tudor S.},
  date = {2013},
  publisher = {Springer Science \& Business Media},
  edition = {2nd},
  isbn = {978-1-4757-2435-8},
  abstract = {This text provides an introduction to the geometry of the motion of particles and rigid bodies and the Lagrangian and Hamiltonian formalisms.}
}

@article{famaCommonRiskFactors1993,
  title = {Common Risk Factors in the Returns on Stocks and Bonds},
  author = {Fama, Eugene F. and French, Kenneth R.},
  date = {1993},
  journaltitle = {Journal of Financial Economics},
  volume = {33},
  number = {1},
  pages = {3--56},
  doi = {10.1016/0304-405X(93)90023-5},
  abstract = {This paper identifies five common risk factors in the returns on stocks and bonds. There are three stock-market factors: an overall market factor and factors related to firm size and book-to-market equity.}
}

@article{harveyDynamicFactorModels1989,
  title = {Dynamic {{Factor Models}} and the {{Forecasting}} of {{National Account Time Series}}},
  author = {Harvey, Andrew C. and Koopman, Siem Jan},
  date = {1989},
  journaltitle = {Journal of Applied Econometrics},
  volume = {4},
  number = {3},
  pages = {201--233},
  doi = {10.1002/jae.**********},
  abstract = {Dynamic factor models provide a way of handling the curse of dimensionality when modelling large systems of time series.}
}

@article{kalmanNewApproachLinear1960,
  title = {A {{New Approach}} to {{Linear Filtering}} and {{Prediction Problems}}},
  author = {Kalman, Rudolph E.},
  date = {1960},
  journaltitle = {Journal of Basic Engineering},
  volume = {82},
  number = {1},
  pages = {35--45},
  doi = {10.1115/1.3662552},
  abstract = {The classical filtering and prediction problem is re-examined using the Bode-Shannon representation of random processes and the state-transition method of analysis of dynamic systems.}
}

@article{chenPhysicsInformedNeuralNetworks2021,
  title = {Physics-Informed {{Neural Networks}}: {{A Deep Learning Framework}} for {{Solving Forward}} and {{Inverse Problems Involving Nonlinear Partial Differential Equations}}},
  author = {Raissi, Maziar and Perdikaris, Paris and Karniadakis, George Em},
  date = {2019},
  journaltitle = {Journal of Computational Physics},
  volume = {378},
  pages = {686--707},
  doi = {10.1016/j.jcp.2018.10.045},
  abstract = {We introduce physics-informed neural networks – neural networks that are trained to solve supervised learning tasks while respecting any given laws of physics described by general nonlinear partial differential equations.}
}

@article{blackScholesPricingOptions1973,
  title = {The {{Pricing}} of {{Options}} and {{Corporate Liabilities}}},
  author = {Black, Fischer and Scholes, Myron},
  date = {1973},
  journaltitle = {Journal of Political Economy},
  volume = {81},
  number = {3},
  pages = {637--654},
  doi = {10.1086/260062},
  abstract = {This paper presents a model of option pricing which is based on the premise that prices of options are determined by the condition that no profits can be made by trading in the option and the stock.}
}

@book{duffiePortfolioChoiceAsset2001,
  title = {Dynamic {{Asset Pricing Theory}}},
  author = {Duffie, Darrell},
  date = {2001},
  publisher = {Princeton University Press},
  edition = {3rd},
  isbn = {978-0-691-09022-3},
  abstract = {This book is a comprehensive treatment of the theory and empirics of asset pricing, with an emphasis on dynamic models.}
}

@article{markowitz1952portfolio,
  title = {Portfolio {{Selection}}},
  author = {Markowitz, Harry},
  date = {1952},
  journaltitle = {The Journal of Finance},
  volume = {7},
  number = {1},
  pages = {77--91},
  doi = {10.1111/j.1540-6261.1952.tb01525.x},
  abstract = {The process of selecting a portfolio may be divided into two stages: (1) the stage which starts with observation and experience and ends with beliefs about the future performances of available securities; (2) the stage which starts with the relevant beliefs about future performances and ends with the choice of portfolio.}
}

@article{kellyNewInterpretationInformation1956,
  title = {A {{New Interpretation}} of {{Information Rate}}},
  author = {Kelly, John L.},
  date = {1956},
  journaltitle = {Bell System Technical Journal},
  volume = {35},
  number = {4},
  pages = {917--926},
  doi = {10.1002/j.1538-7305.1956.tb03809.x},
  abstract = {The author proposes a new method for determining the capacity of a noisy discrete channel, based on the optimization of the exponential rate of growth of wealth in repeated investment situations.}
}

@article{ljungSystemIdentificationToolbox1999,
  title = {System {{Identification}}: {{Theory}} for the {{User}}},
  author = {Ljung, Lennart},
  date = {1999},
  journaltitle = {PTR Prentice Hall Information and System Sciences Series},
  edition = {2nd},
  publisher = {Prentice Hall PTR},
  isbn = {978-0-13-656695-3},
  abstract = {System Identification: Theory for the User provides a comprehensive treatment of the theory and practice of system identification.}
}

@article{rollCritiqueCAPMTests1977,
  title = {A {{Critique}} of the {{Asset Pricing Theory}}'s {{Tests Part I}}: {{On Past}} and {{Potential Testability}} of the {{Theory}}},
  author = {Roll, Richard},
  date = {1977},
  journaltitle = {Journal of Financial Economics},
  volume = {4},
  number = {2},
  pages = {129--176},
  doi = {10.1016/0304-405X(77)90009-5},
  abstract = {The theory is not testable unless the exact composition of the true market portfolio is known and used in the tests. This implies that the theory is not testable unless all individual assets are included in the sample.}
}
@article{lange2024bellman,
  title={Bellman filtering and smoothing for state--space models},
  author={Lange, Rutger-Jan},
  journal={Journal of Econometrics},
  volume={238},
  number={2},
  pages={105632},
  year={2024},
  publisher={Elsevier}
}
#import "@preview/charged-ieee:0.1.3": ieee
#import "@preview/algorithmic:1.0.3"

// Modern font overrides and spacing settings
#set text(font: ("Open Sans", "Liberation Sans", "Nimbus Sans", "FreeSans"), size: 10pt)
#show raw: set text(font: ("Iosev<PERSON>", "Liberation Mono", "Nimbus Mono PS", "FreeMono"))

// Improve paragraph spacing and line height
#set par(leading: 0.7em, spacing: 1.2em)
#set block(spacing: 1.5em)

// Better heading spacing
#show heading.where(level: 1): set block(above: 2em, below: 1.5em)
#show heading.where(level: 2): set block(above: 1.8em, below: 1.2em)
#show heading.where(level: 3): set block(above: 1.5em, below: 1em)
#show heading.where(level: 4): set block(above: 1.2em, below: 0.8em)

#show: ieee.with(
  title: [The Structure of Strategy: A First-Principles Approach to Discovering the Dynamics of Active Economic Agents],
  authors: (
    (
      name: "<PERSON><PERSON><PERSON>",
      department: [DCSC],
      organization: [TU Delft],
      location: [Netherlands],
      // email: "givan.com"
    ),
  ),
  abstract: [
    We develop and validate a novel paradigm for discovering the first-principles laws governing active ETF behavior, motivated by the "Residual Diagnostics Puzzle" from my prior Master's thesis. This puzzle revealed that while sophisticated statistical models can capture complex dynamics, they often do so at the cost of interpretability, creating a "black box" of latent factors. Our approach directly addresses this interpretability-complexity trade-off. We make a strategic paradigm separation: we model the *Agent* (ARKK) from first principles using Port-Hamiltonian Systems (PHS) for structural insight, while treating the *Market* as a complex environment using a statistical Dynamic Factor Stochastic Volatility (DFSV) model.

    Our methodology provides complete validation through three phases: *Phase 1A* (Linear De-coupling Gateway) proves that agent parameters can be recovered even in challenging coupled systems, validating the fundamental inverse problem. *Phase 1B* (Sensor-Actuator Discovery Loop Rehearsal) provides a perfect dress rehearsal using synthetic data with strict one-way information flow (Market → Agent), validating both the nonlinear discovery algorithm and the core modeling assumption. *Phase 2* applies this fully-validated methodology to real data through a simple but profound data swap: synthetic observations become real ARKK holdings, synthetic market signals become DFSV-generated market factors.

    We establish the first rigorously validated framework for discovering interpretable, first-principles laws in complex financial systems. We prove both the algorithmic approach and the underlying modeling assumptions sound before applying them to real-world data.
  ],
  bibliography: bibliography("refs.bib"),
)


= Core Research Motivation

== The Problem: The "Residual Diagnostics Puzzle"

My previous Master's thesis revealed a fundamental limitation in financial modeling. Even sophisticated Dynamic Factor Stochastic Volatility (DFSV) models estimated with sophisticated filtering techniques can falter at the individual asset level. The model successfully captured aggregate market dynamics but failed standard diagnostic tests for conditional heteroskedasticity. This failure demonstrates that while statistical models can be extended to capture more complexity, they often do so at the cost of interpretability, creating a 'black box' of latent factors. This puzzle demands a solution that prioritizes structural insight over statistical brute force.

== The Critical Distinction: From Market Structure to Agent Behavior

The "agents" in my prior thesis research differ fundamentally from the agent in this proposal. Fama-French 10x10 portfolios represent passive market segments, while ARKK represents an active strategic entity. This distinction represents a logical escalation in research complexity and impact.

*Fama-French Portfolios (Prior Research):*
- *Nature:* Passive, rebalanced portfolios representing market segments or styles
- *Dynamics:* Time-series reflect performance of market segments, not deliberate decisions
- *Puzzle:* Unexplained idiosyncratic volatility in market structure after factor modeling
- *Implication:* Even passive constructs exhibit complex dynamics beyond standard models

*ARKK (Current Research):*
- *Nature:* Single, actively managed ETF making deliberate, strategic trading decisions
- *Dynamics:* Holdings changes reflect active portfolio manager decision-making process
- *Challenge:* Understanding the first-principles laws governing strategic agent behavior
- *Opportunity:* Model internal decision-making structure, not just statistical properties

The DFSV failure on passive portfolios exposes a critical gap in financial modeling. It demonstrates that statistical models face a fundamental trade-off between complexity and interpretability; in the quest to capture more dynamics, they become 'black boxes'. Active agents, with their deliberate strategic actions, therefore demand a fundamentally different approach that prioritizes structural insight. This challenge justifies our shift to first-principles Economic Engineering to model internal states, strategic objectives, and decision-making processes directly.


== Missing Components in Current Approaches

Current portfolio models suffer from three critical limitations.
First, models create an interpretability crisis through oversimplified factor models or black-box machine learning approaches that offer no structural insights. Second, traditional approaches exhibit dynamic blindness by failing to capture the interplay between idealized strategy objectives, real-world frictions, and external market forces. Third, existing methods demonstrate practical irrelevance by providing no actionable insights for cost optimization, risk management, or strategy refinement.


== The Challenge Chain: From Misspecification to Discovery

Solving the agent behavior puzzle requires overcoming three interlinked challenges:

1. *The Latent State Problem:* First-principles Port-Hamiltonian models require the agent's internal economic momentum ($p_("agent")$), the reservation price that drives trading decisions. This critical state variable is unobservable.

2. *The Endogeneity Trap:* Observable market prices cannot proxy for internal valuations. This methodological error makes separating internal strategy from market reactions impossible.

3. *The Chicken-and-Egg Problem:* Physical laws ($H, R, g$) cannot be identified without state trajectories ($p_("agent")$), yet state trajectories cannot be inferred without knowing the governing physical laws.


== The Opportunity: An Iterative, Self-Consistent Solution

We solve this challenge chain with a principled mathematical framework that captures both statistical regularities and strategic decision-making logic. Port-Hamiltonian Systems provide:

- Guaranteed stability through Hamiltonian structure
- Explicit modeling of energy dissipation (transaction costs, slippage)
- Structured incorporation of external forces (market volatility, fund flows)
- Transparent decomposition of portfolio dynamics

Our solution employs a hybrid, iterative discovery pipeline using an Expectation-Maximization (EM)-like algorithm that breaks the chicken-and-egg deadlock.


= Theoretical Foundation & Framework

== The Research Progression: Escalating Complexity and Impact

This thesis escalates from market structure analysis to agent behavior modeling through four critical steps:

1. *Prior Research Insight:* Even passive portfolios exhibit complex dynamics that defeat state-of-the-art statistical models
2. *Current Research Question:* What first-principles laws govern an active, strategic agent?
3. *Methodological Innovation:* We shift from phenomenological modeling (DFSV) to first-principles discovery (PHS + ML)
4. *Strategic Case Selection:* ARKK provides the ideal test case as a high-profile, actively managed ETF with clear strategic focus

This progression transforms a market structure puzzle into a behavioral discovery framework with broad applicability.


== Economic Engineering Mapping to PHS

We establish a direct mapping between Economic Engineering and active ETF dynamics @mendelNewtonianMechanicsDemand2023:

#figure(
  table(
    columns: 4,
    stroke: 0.5pt,
    table.header([*Category*], [*PHS Component*], [*ETF Interpretation*], [*Mechanical Analogy*]),
    [*State (Stocks)*], [$q(t)$], [Asset Holdings], [*Position*],
    [], [$p(t)$], [Internal Reservation Price], [*Momentum*],
    [*Dynamics (Flows)*], [$dot(q)(t)$], [Trading Velocity], [*Velocity*],
    [], [$dot(p)(t)$], [Price Adjustment Rate], [*Force*],
    [*Internal Physics*], [$H(q,p)$], [Economic Surplus], [*Total Energy*],
    [], [$R(q,p)$], [Frictions (Costs, Slippage)], [*Damping Matrix*],
    [*External Interaction*], [$u(t)$], [Market Factors & Fund Flows], [*External Forces*],
    [], [$g(q,p)$], [Input Coupling], [*Input Matrix*],
  ),
  caption: [Economic Engineering to PHS Mapping]
)

== The Complete PHS Model

We implement a strategic separation of concerns using Economic Engineering and Port-Hamiltonian Systems:

1. *The Market Model (Phenomenological):* We characterize the market as a complex, external environment using high-fidelity DFSV statistical modeling. External inputs are $u(t) = [f_t^T, h_t^T, u_("fund")^T]^T$, where $f_t$ are latent factor returns, $h_t$ are log-volatilities, and $u_("fund")$ represents fund-specific inputs (fund flows, strategy changes).

2. *The Agent Model (First-Principles):* We model the active ETF as a Port-Hamiltonian System with dynamics:

$ dot(z) = (J - R(z)) nabla H(z) + g(z) u(t) $

where $z = [q^T, p^T]^T$ represents holdings and economic momentum (internal reservation prices), and $J$ is the canonical symplectic matrix $J = mat(0, I; -I, 0)$ with $I$ being the $n times n$ identity matrix ($n$ = number of assets).


== A Strategic Separation of Paradigms: The Agent and its Environment

We make a fundamental strategic choice in modeling philosophy: we treat the *Agent* (ARKK) and the *Market* with different paradigms, each appropriate to their nature and the research objectives.

=== The Agent: First-Principles Modeling with Port-Hamiltonian Systems

The active ETF agent is modeled from first principles using Port-Hamiltonian Systems because:

1. *Single Entity with Defined Objective:* Unlike the market, the ETF represents a single decision-making entity with a discoverable, coherent strategy that can be expressed as an optimization problem.

2. *Interpretable Internal States:* The agent's internal reservation prices (economic momentum $p$) and holdings ($q$) form natural conjugate variables that capture the essence of strategic decision-making.

3. *Physical Guarantees:* PHS structure ensures stability and passivity, preventing unphysical solutions while maintaining economic realism.

=== The Market: Statistical Modeling with DFSV

We treat the market environment as a complex system best represented statistically because:

1. *Intractable Complexity:* A first-principles model of the entire market would require modeling millions of interacting agents. This approach is computationally and theoretically intractable.

2. *Observable Regularities:* Despite its complexity, the market exhibits statistical regularities (factors, volatility clustering) that can be captured by sophisticated econometric models.

3. *Phenomenological Appropriateness:* The agent's decision-making depends on the market's observable statistical behavior, not its underlying micro-structure.

=== Two System Architectures Arising from this Paradigm Separation

This modeling philosophy gives rise to two key system structures used in different phases:

==== The "White-Box" Coupled System (Phase 1A)
For validation purposes, we model both agent and Market Maker as coupled PHS subsystems where all laws are known. This creates an ideal "laboratory" environment for proving structural identifiability and validating the decoupling methodology.

==== The "Grey-Box" Sensor-Actuator System (Phases 1B & 2)  
For real-world application, the ETF operates as a sensor-actuator agent:

*Sensing:* The ETF observes market state through DFSV-extracted factors $u(t) = [f_t^T, h_t^T, u_("fund")^T]^T$

*Actuating:* The ETF's output is its trading velocity $dot(q)$, assumed absorbed by the market without affecting the broad systematic factors

This asymmetric interaction justifies the clean separation into internal dynamics $(J - R)∇H$ and external reactions $g(z)u(t)$, where discovering $g(z)$ reveals the agent's dynamic response policy to market conditions.

*Key Interpretation:* The choice of $q$ (asset quantities) and $p$ (economic momentum) as conjugate variables follows from Economic Engineering principles. The canonical momentum $p$ represents the agent's internal reservation price, distinct from external market prices. The Hamiltonian $H(q,p)$ depends only on the agent's internal state, ensuring clean separation of the agent's internal objective function from its market interaction.


== Key Theoretical Assumptions

*Informational Sufficiency Assumption*: This framework assumes that the dynamic covariance structure of the S&P 500 constituents contains sufficient information for the DFSV-BIF engine to identify and estimate the latent systematic factors ($f_t, h_t$) that govern risk pricing within that universe. This assumption acknowledges the limitations of any finite market proxy while emphasizing the power of the estimation methodology to extract meaningful systematic risk factors from high-dimensional, though incomplete, financial data. The success of this approach rests not on the impossible claim that the S&P 500 represents the complete market portfolio (which Roll's Critique @rollCritiqueCAPMTests1977 has shown to be untestable), but rather on the demonstrated ability of the Bellman Filter @lange2024bellman to recover true latent factors from rich but imperfect data sources.

*Hyperparameter Stationarity of the Market Model*: While the DFSV model is explicitly designed to capture non-stationarity in market risk through its time-varying factor volatilities ($h_t$), it relies on the assumption that the parameters of the underlying stochastic processes (e.g., the VAR matrices $Φ_f$, $Φ_h$) are themselves stable over the analysis period. Our approach significantly alleviates the fragility of a naive stationarity assumption by modeling volatility dynamics directly, thereby transforming market regime shifts from unhandled structural breaks into tractable features of the input signal fed to the agent model. The remaining risk of a true hyperparameter shift is mitigated by explicitly testing the final discovered laws for regime stability.


= Methodology: A Three-Phase Validation and Discovery Framework

*Objective:* To rigorously validate the entire discovery pipeline in controlled environments before application to real data, ensuring the methodology is sound and the results are trustworthy.

== A. The Core Discovery Engine: The Hamiltonian Decomposition Curriculum (HDC)

We solve the chicken-and-egg problem of simultaneously discovering physical laws and latent states through a hierarchical, three-step curriculum. This approach prevents degenerate solutions by forcing each component to be discovered under conditions that isolate its proper physical role. The curriculum progresses from a conservative core (H) to internal frictions (R) and finally to external market reactions (g).

The HDC is a hierarchical discovery strategy with three core steps:
1. *Conservative Core Discovery ($H$):* Force the Hamiltonian to be maximally expressive by constraining $R = 0, g = 0$.
2. *Dissipative Dynamics Discovery ($R$):* Discover friction $R$ while using a "data cleaning" step to prevent bias.
3. *External Coupling Discovery ($g$):* Discover the agent's market reaction policy $g(z)$ and perform final system optimization.

=== High-Level Algorithm Structure

#algorithmic.algorithm({
  import algorithmic: *
  Function("HamiltonianDecompositionCurriculum", ($q_"obs"$, $u(t)$), {
    Comment[Bootstrap initial momentum $p_0$]
    
    Comment[Step 1: Conservative Core Discovery]
    Assign[($H_("conservative"), p_("conservative")$)][Call("ConservativeCoreEM")($q_"obs"$, $p_0$)]
    
    Comment[Step 2: Dissipative Dynamics Discovery]
    Assign[($H_("dissipative"), R_("dissipative"), p_("dissipative")$)][Call("DissipativeDynamicsEM")($q_"obs"$, $H_("conservative")$, $p_("conservative"$)]
    
    Comment[Step 3: External Coupling Discovery]
    Assign[($H_"final", R_"final", g_"final", p_"final"$)][Call("ExternalCouplingEM")($q_"obs"$, $u(t)$, $H_("dissipative")$, $R_("dissipative")$, $p_("dissipative"$)]
    
    Return[($H_"final", R_"final", g_"final", p_"final"$)]
  })
})

=== Step 1: Conservative Core Discovery

*Goal:* Force the Hamiltonian to be maximally expressive by constraining $R = 0, g = 0$.

*Key Insight:* Without this constraint, the HNN will learn a trivial function while symbolic terms do all the work. By eliminating non-conservative escape routes, we ensure $H^*$ captures the agent's true energy landscape.

*EM Structure:* The algorithm alternates between training the HNN to minimize dynamics prediction error (M-step) and re-estimating the latent momentum trajectory using an Extended Kalman Filter given the updated Hamiltonian (E-step). This iterative process ensures self-consistency between the learned energy function and the inferred hidden states.

=== Step 2: Dissipative Dynamics Discovery

*Goal:* Discover friction $R$ while eliminating bias from the initial conservative-only Hamiltonian.

*Critical Innovation:* The "data cleaning" step. This prevents error propagation by ensuring the Hamiltonian is only asked to explain the conservative portion of the dynamics. The training target for the HNN is adjusted to $dot(z)_"obs" + R_k nabla H_(k-1)$. This achieves true separation of conservative vs. dissipative effects.

*EM Structure:* Each iteration discovers friction laws using PySR (M-step for $R$), fine-tunes the Hamiltonian with regularization toward the Step 1 solution (M-step for $H$), then re-estimates latent states using the updated dissipative model (E-step). The cleaning step ensures clean attribution between energy and friction.

=== Step 3: External Coupling Discovery

*Goal:* Discover the agent's market reaction policy $g(z)$ and perform final system optimization.

*Economic Significance:* The discovered $g$ matrix reveals how Market Engine factors ($u(t)$) translate into internal forces on the agent's valuation, essentially the agent's dynamic response strategy to market conditions.

*EM Structure:* The final phase discovers coupling functions using PySR to explain remaining residuals (M-step for $g$), performs joint fine-tuning of all parameters with regularization (M-step for $H,R$), and estimates the complete latent state trajectory incorporating all discovered dynamics (E-step). This produces the final self-consistent physical laws.

=== Methodological Advantages

This Hamiltonian Decomposition Curriculum (HDC) structure provides several critical advantages over naive approaches:

1. *Bias Prevention:* Each phase prevents the previous phase's approximations from contaminating subsequent discoveries
2. *Physical Interpretability:* Each component $(H, R, g)$ is discovered under conditions where it can serve its proper physical role
3. *Algorithmic Stability:* Each phase converges to a well-defined optimum before proceeding to increased complexity
4. *Economic Coherence:* The progression from conservative core → internal frictions → external reactions mirrors the natural hierarchy of economic decision-making

=== Implementation Details

*Dimensional Feasibility:* A naive approach to discovering the $2N times 2N$ friction matrix $R(z)$ and $2N times K$ coupling matrix $g(z)$ would require PySR to discover thousands of equations simultaneously, computationally intractable. However, PHS imposes powerful structural constraints that make this feasible:

1. *Structured Dissipation ($R$):* Economic frictions are local to each asset and primarily resist motion. This reduces discovery from a dense $2N times 2N$ matrix to $N$ independent scalar friction functions $r_i (q_i, p_i)$.

2. *Structured Coupling ($g$):* Market Engine forces act on agent valuation (momentum $p$) for each asset individually. This reduces discovery from a dense $2N times K$ matrix to $N times K$ independent scalar policy functions $g_(i,k) (q_i, p_i)$.

These physically and economically motivated structural assumptions transform an impossibly high-dimensional problem into parallel, low-dimensional problems tractable for PySR.

*PySR Model Selection:* Use threshold filtering above acceptable MSE, then select based on PySR's built-in complexity scoring for principled model selection.

*State Estimation:* Extended Kalman Filter for latent momentum trajectory $p$, with UKF fallback if linearization proves inadequate.

*Convergence:* Symbolic law stability, state trajectory convergence (L2-norm < 1e-4), maximum 20 iterations.

This staged approach guides discovery toward physically meaningful solutions where each PHS component serves its intended economic role.

== B. Phase 1A: The Linear De-coupling Gateway

*System:* A "white-box" world of a linear PHS Agent coupled to a linear PHS Market Maker, driven by stochastic noise. This represents the most challenging scenario where bidirectional feedback exists between agent and market.

*System Formulation:*
$ dot(z)_("total") = (J_("total") - R_("total")) nabla H_("total")(z_("total")) + g_("total") u_("ext")(t) $

where $z_("total") = [q_("agent")^T, p_("agent")^T, q_("market")^T, p_("market")^T]^T$ represents the combined state of both agent and Market Maker subsystems.

*Task & Purpose:* Use standard system identification methods (e.g., PEM with a Kalman filter) to prove that the agent's parameters ($theta_("agent")$) are structurally identifiable and can be uniquely recovered even when dynamically coupled. This validates the solvability of the core inverse problem in the presence of feedback, answering the fundamental question: "Is it even possible to untangle the agent from its environment?"

*Success Criterion:* Successful recovery of $theta_("agent,true")$ with statistical confidence bounds, demonstrating that agent-market decoupling is mathematically feasible.

== C. Phase 1B: The Sensor-Actuator Discovery Loop Rehearsal

*What it is:* A perfect, end-to-end rehearsal for the real-world discovery task. This phase transitions from the "white-box" coupled system of Phase 1A to a "grey-box" *sensor-actuator* model.

*How it's different from Phase 1A:* Instead of bidirectional coupling, we now enforce a strict *one-way information flow:* the Market influences the Agent, but the Agent's actions are assumed too small to influence the Market. This is the core assumption we need to validate before applying it to real data.

*The Task:* We create a synthetic environment that perfectly mimics this assumption. A known, non-linear "black-box" PHS agent generates observational data ($q_("obs,synthetic")$) by reacting to an independently generated synthetic market signal ($u_("synthetic")$). The challenge for our *HDC algorithm* is to take only this observational data and market signal and perfectly recover the agent's hidden, ground-truth laws ($H_("true")$, $R_("true")$, $g_("true")$).

*Purpose:* Success here provides a rigorous, end-to-end validation of the entire discovery pipeline under the exact architectural assumptions that will be used in Phase 2. It proves the algorithm works and that the sensor-actuator model is a valid foundation for real-world analysis.

== D. Phase 2: Application to Real-World Data

*What it is:* The application of the fully-validated discovery pipeline to real-world data to discover the unknown first-principles laws governing ARKK's strategic behavior.

*How it's different from Phase 1B:* This phase is mechanically identical to the Phase 1B rehearsal, but with a simple and profound *data swap*. Instead of synthetic data, we use real-world inputs:
- The synthetic observations $q_("obs,synthetic")$ are replaced with *real ARKK holdings data*, $q_("ARKK")$.
- The synthetic market signal $u_("synthetic")$ is replaced with the *real market signal generated by our DFSV Market Engine*, $u_("DFSV")$.

*The Task:* The objective is no longer to recover known laws, but to *discover the unknown laws* ($H_("ARKK")$, $R_("ARKK")$, $g_("ARKK")$) that govern the ARKK ETF. The validated HDC pipeline is applied to the real data, operating under the same one-way sensor-actuator assumption proven sound in Phase 1B.

*Purpose:* To move from methodological validation to scientific discovery, producing an interpretable, first-principles model of a complex, real-world economic agent.

== Benchmark Validation: A Feasible Challenge-Based Approach

To validate our primary DFSV-based model without the prohibitive cost of running a full parallel discovery loop, we employ a "benchmark challenge" strategy. This tests whether a simpler, non-structural market model could provide an equally good explanation for the agent's behavior.

1. *Primary Model Discovery:* First, the full HDC is executed using the DFSV factors ($u_"DFSV"$) to produce the final discovered physics ($H^*$, $R^*$, $g^*_"DFSV"$) and agent state ($z^*_"agent"$). This is our primary "hero" model.

2. *Benchmark Market Engine Characterization:* A standard *DCC-GARCH model* is estimated for the S&P 500. Principal Component Analysis is then applied to the standardized returns to extract a set of atheoretical "DCC factors," $u_"DCC"$.

3. *The Challenge:* We assume the agent's internal physics ($H^*$, $R^*$) are fundamental properties and hold them fixed. We then perform a *single-pass discovery* using PySR to find the best possible reaction policy $g^*_"DCC"$ that links the agent's residual dynamics to the simpler $u_"DCC"$ market factors.

4. *Comparison:* The two models $(H^*, R^*, g^*_"DFSV", u_"DFSV")$ and $(H^*, R^*, g^*_"DCC", u_"DCC")$ are then compared on three criteria: *in-sample goodness of fit, parsimony and interpretability of the discovered $g$ term, and out-of-sample forecasting performance.* This provides a rigorous yet computationally feasible method for validating the explanatory power of the structural DFSV Market Engine representation.


= Technology Stack & Implementation

== Why Port-Hamiltonian Systems?

*Interpretable Structure*: PHS provides direct economic mapping where $H$ represents Economic Surplus, $R$ models frictions, and $g$ captures external forces. The equation $dot(z) = (J-R) nabla H + g u$ enforces principled separation of conservative, dissipative, and external dynamics, enabling the targeted discovery strategies outlined in our methodology.

*Physical Guarantees*: Bounded $H$ and positive semi-definite $R$ ensure passivity and stability, preventing unphysical solutions while maintaining economic realism. These constraints are crucial for the symbolic regression step, as they provide natural regularization.

== Core Technology Stack

This research integrates three advanced methodologies into a single, cohesive pipeline, with Python/JAX as the unifying implementation language:

- *DFSV-BIF (The Market Engine):* Leveraging the validated framework from prior research provides robust market state characterization through latent factor extraction
- *PHS/HNN (The Agent Engine):* Port-Hamiltonian Systems with Hamiltonian Neural Networks @greydanusHamiltonianNeuralNetworks2019a for learning complex, non-linear internal objective functions while preserving energy conservation
- *PySR (The Discovery Engine):* Symbolic regression @cranmerInterpretableMachineLearning2023 for discovering interpretable laws governing frictions and market coupling, with built-in complexity management

== Implementation Approach

*Hybrid System Identification:* The methodology requires solving the *inverse problem* in Economic Engineering, discovering internal physics from external behavior. This demands automatic differentiation (JAX), robust ODE solvers (Diffrax), and symbolic mathematics (PySR/SymPy) working in concert.

*Modular Architecture:* Each phase of the nested EM curriculum requires different computational capabilities, necessitating a flexible implementation that can switch between HNN training, symbolic regression, and Kalman filtering seamlessly.

= Expected Outcomes & Contributions

== Scientific Contributions

1. *Methodological Innovation*: First validated *three-phase* approach for PHS system identification in finance: a three-phase framework that first proves the *de-coupling solvability* of coupled systems, then validates the *non-linear discovery algorithm*, before application to real-world data.
2. *Novel Framework*: First application of learned PHS models with HNN/PySR to active ETF management
3. *Empirical Grounding*: Connects Economic Engineering theory to real financial market data
4. *Interpretable ML*: Demonstrates physics-informed constraints maintaining interpretability

== Practical Impact

1. *Validated Pipeline*: Robust methodology applicable to other financial systems
2. *Cost Attribution*: Quantified understanding of ETF transaction cost structure
3. *Strategy Insights*: Data-driven revelation of ETF's true Economic Surplus function
4. *Risk Management*: Transparent decomposition of conservative vs. dissipative dynamics

= Discussion Points

== 1. Theoretical Validation & Economic Interpretability

*Question:* How can we ensure the learned Hamiltonian $H(q,p)$ truly represents economic surplus rather than just a mathematical fit?

*Proposed Approach:* Validate against known portfolio theory principles (mean-variance optimization @markowitz1952portfolio, Kelly criterion @kellyNewInterpretationInformation1956) and incorporate economic constraints in discovery process. In addition to validating against known portfolio theory, the economic interpretability is structurally enforced and validated through the *Hamiltonian Decomposition Curriculum*. Each component (`H`, `R`, `g`) is discovered in a phase that isolates its specific economic role (conservative value, frictional costs, reaction to external forces).

== 2. Convergence of the EM Loop

*Question:* How can we formally assess convergence of the iterative procedure?

*Proposed Approach:* Multi-criteria convergence: stability in symbolic laws, minimal change in $p_("agent")$ trajectory (L2-norm < 1e-4), maximum iteration limit (20). Convergence will be formally assessed via multi-criteria checks. Furthermore, the convergence properties of the entire EM loop will be *rigorously validated and stress-tested in Phase 1B* on synthetic data, where the ground-truth is known, before being applied to real data in Phase 2.

== 3. Informational Sufficiency Validation

*Question:* How can we validate that the DFSV-extracted factors $(f_t, h_t)$ contain sufficient information to characterize the systematic risk environment facing ARKK?

*Proposed Approach:* (1) Diagnostic tests on final PHS model residuals to detect remaining systematic patterns that would indicate missing market information, (2) Sensitivity analysis varying the number of extracted factors to assess robustness, (3) Out-of-sample performance comparison with models incorporating additional market variables, (4) Economic interpretation of any unexplained agent dynamics as truly idiosyncratic behavior vs. missing systematic factors.

= Risk Mitigation & Feasibility

== Technical Risks

- *EM Loop Convergence Failure:* Mitigated through a *three-phase validation framework.* The core algorithm's convergence is proven in a controlled environment in *Phase 1B* before being risked on real data. Additionally, standard techniques like damped updates and modular testing will be used.
- *HNN Training Instability:* Mitigated through the *three-phase validation framework.* The HNN's behavior and stability will be fully characterized in a controlled environment in *Phase 1B* before being risked on real data.
- *PySR Discovery Issues:* Mitigated through the *three-phase validation framework.* The ability of PySR to discover the correct symbolic laws will be proven in a controlled environment in *Phase 1B* before being risked on real data.
- *Bootstrap Initialization Sensitivity:* Mitigated through the *three-phase validation framework.* The sensitivity to the initial $p_0$ guess is tested in *Phase 1B*, allowing for the selection of the most robust bootstrap method before application to real data.

== Computational Complexity

- *High-Dimensional Holdings:* Leverage dimensionality reduction via PCA or structural simplification
- *EM Loop Intensity:* Modular design with clear convergence criteria
- *DFSV Preprocessing:* One-time computation (1 day), each EM iteration (minutes to hours)

= Key Milestones & Timeline

== Phase 1A: The Linear De-coupling Gateway

- *Deliverable:* Validated linear PEM implementation proving structural identifiability in coupled PHS systems.
- *Success Metric:* Recovery of $theta_("agent,true")$ with statistical confidence even under bidirectional feedback.
- *Impact:* Validates that the fundamental inverse problem is solvable, justifying the sensor-actuator assumption.

== Phase 1B: The Sensor-Actuator Discovery Loop Rehearsal

- *Deliverable:* Complete validation of the *Hamiltonian Decomposition Curriculum (HDC)* on synthetic sensor-actuator data.
- *Success Metric:* Perfect recovery of ground-truth laws ($H_("true")$, $R_("true")$, $g_("true")$) within tolerance bounds.
- *Impact:* Validates both the core discovery algorithm and the sensor-actuator modeling assumption for Phase 2.

== Phase 2: Application to Real-World Data

- *Step 1 Deliverable (Market Characterization):* Generation of the high-fidelity market signal $u_("DFSV")$ from real S&P 500 data.
- *Step 2 Deliverable (First-Principles Discovery):* Successful execution of the validated HDC pipeline on real ARKK data; initial discovery of $H^*$, $R^*$, $g^*$.
- *Success Metric:* Convergent algorithm with economically interpretable discovered laws.
- *Final Deliverable:* Full economic interpretation of discovered laws, benchmark comparisons, and final validation against statistical models, demonstrating superior explanatory power.

== Final Phase (Thesis Completion)

- *Documentation:* Comprehensive thesis writing and software documentation.
- *Validation:* Cross-platform verification, additional ETF testing.
- *Publication:* Prepare results for academic venues.

Extension opportunities include connections to broader Economic Engineering research and future research directions, with validation from domain experts and market practitioners.

*Next Steps:*
2. Begin linear PEM implementation and synthetic data generation
3. Develop modular architecture supporting both phases
4. Schedule regular progress meetings (bi-weekly)
5. Establish collaboration channels with relevant research groups

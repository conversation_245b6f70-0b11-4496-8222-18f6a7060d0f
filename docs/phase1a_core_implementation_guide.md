# Phase 1A Core Implementation Guide: Linear De-coupling Gateway

This guide walks you through implementing the symbolic Port-Hamiltonian System foundation in `src/phynance/core/` that will support Phase 1A's "Linear De-coupling Gateway" - proving that agent parameters can be recovered from coupled systems using standard system identification methods.

## Implementation Sequence

### Step 1: Extend `hamiltonian_base.py`

**Goal:** Add symbolic quadratic Hamiltonian support for linear PHS systems.

**Location:** `src/phynance/core/hamiltonian_base.py`

**What to add:**

```python
import sympy as sp
from typing import Dict, List, Optional

class SymbolicQuadraticHamiltonian(HamiltonianBase):
    """
    Symbolic quadratic Hamiltonian H(z) = 0.5 * z^T * M * z
    
    For white-box coupled agent-market system where both subsystems are PHS:
    z_total = [q_agent, p_agent, q_market, p_market]^T
    θ_agent = {m_agent, k_agent, ...}
    θ_market = {m_market, k_market, ...}
    """
    
    def __init__(self, state_names: List[str], param_names: List[str]):
        """
        Args:
            state_names: ['q_agent', 'p_agent', 'q_market', 'p_market']
            param_names: ['m_agent', 'k_agent', 'm_market', 'k_market', ...]
        """
        
    def get_symbolic_matrix(self) -> sp.Matrix:
        """Return symbolic M matrix as function of parameters."""
        
    def get_symbolic_gradient(self) -> sp.Matrix:
        """Return symbolic ∇H = M * z."""
        
    def substitute_parameters(self, param_values: Dict[str, float]) -> np.ndarray:
        """Convert symbolic M to numerical matrix."""
```

**Key Implementation Notes:**
- Use SymPy symbols for both states `z_total` and parameters `θ_agent`, `θ_market`
- Build the M matrix symbolically for the total coupled system
- Both agent and market have their own kinetic + potential energy terms
- System is fully coupled through shared energy landscape

### Step 2: Extend `dissipation_base.py`

**Goal:** Add symbolic dissipation matrix with coupling terms.

**Location:** `src/phynance/core/dissipation_base.py`

**What to add:**

```python
class SymbolicDissipationMatrix(DissipationBase):
    """
    Symbolic dissipation matrix R_total(θ) ensuring positive semi-definite constraint.
    
    For white-box coupled system: R includes bidirectional coupling between 
    agent and Market Maker subsystems. This creates the challenging scenario
    where feedback exists between agent and market.
    """
    
    def __init__(self, state_names: List[str], param_names: List[str]):
        """Initialize with coupling structure."""
        
    def get_symbolic_matrix(self) -> sp.Matrix:
        """Return symbolic R matrix with coupling terms."""
        
    def verify_positive_semidefinite(self) -> bool:
        """Symbolically verify R ⪰ 0 constraint."""
        
    def get_coupling_terms(self) -> Dict[str, sp.Expr]:
        """Extract agent-market coupling coefficients."""
```

**Key Implementation Notes:**
- R_total matrix has bidirectional agent-Market Maker coupling terms
- Must maintain positive semi-definite constraint for physical realizability
- This coupling creates the most challenging identifiability scenario
- Success here validates that agent parameters are recoverable even with feedback

### Step 3: Create `symbolic_utils.py`

**Goal:** Utility functions for symbolic PHS operations.

**Location:** `src/phynance/core/symbolic_utils.py` (new file)

**What to implement:**

```python
import sympy as sp
from typing import List, Dict, Tuple

def construct_phs_state_matrix(
    J_total: sp.Matrix, 
    R_total: sp.Matrix, 
    M_total: sp.Matrix
) -> sp.Matrix:
    """
    Compute A_total = (J_total - R_total) @ M_total for coupled PHS state-space form.
    
    Returns symbolic state matrix A_total(θ_agent, θ_market).
    """

def construct_observability_matrix(
    A: sp.Matrix, 
    C: sp.Matrix, 
    n_states: int
) -> sp.Matrix:
    """
    Construct observability matrix O = [C; CA; CA²; ...; CA^(n-1)].
    
    Returns symbolic observability matrix.
    """

def check_observability_rank(O: sp.Matrix) -> Tuple[int, List[sp.Expr]]:
    """
    Compute symbolic rank of observability matrix.
    
    Returns:
        (rank, list of parameter constraints for full rank)
    """

def construct_transfer_function(A: sp.Matrix, C: sp.Matrix) -> sp.Matrix:
    """
    Compute transfer function G(s) = C(sI - A)^(-1).
    
    For identifiability analysis.
    """

def extract_transfer_coefficients(G: sp.Matrix) -> Dict[str, sp.Expr]:
    """
    Extract polynomial coefficients from transfer function.
    
    Returns coefficient expressions in terms of parameters θ.
    """
```

### Step 4: Extend `phs_base.py` for Diffrax Integration

**Goal:** Add Diffrax-compatible methods and symbolic capabilities to base class.

**Location:** `src/phynance/core/phs_base.py`

**What to add:**

```python
class PHSBase(ABC):
    # ... existing methods ...
    
    def diffrax_dynamics(self, t, z, args):
        """
        Diffrax-compatible dynamics wrapper for ODE-SDE integration.
        
        Args:
            t: Time
            z: State vector
            args: Dictionary containing {'u': input, 'params': parameters}
        """
        u = args.get('u', None)
        params = args['params']
        return self.dynamics(t, z, u, params)
        
    def get_symbolic_state_matrix(self, params: PHSParameters) -> Optional[sp.Matrix]:
        """Get symbolic state matrix A(θ) = (J - R) @ M for coupled system."""
        return None
        
    def analyze_observability(self) -> Optional[Dict[str, Any]]:
        """Perform symbolic observability analysis for gateway validation."""
        return None
```

### Step 5: Create `src/phynance/identification/identifiability_analyzer.py`

**Goal:** Implement the formal gateway analysis - the prerequisite theoretical validation before PEM application.

**Location:** `src/phynance/identification/identifiability_analyzer.py` (new file)

**What to implement:**

```python
class IdentifiabilityAnalyzer:
    """
    Formal structural identifiability analysis for Phase 1A gateway validation.
    
    Implements the two critical mathematical conditions:
    1. Observability: Can you see the state?
    2. Structural Identifiability: Can you find the parameters?
    """
    
    def __init__(self, coupled_phs_system: PHSBase):
        """Initialize with symbolic coupled PHS system."""
        
    def gateway_analysis(self) -> Dict[str, Any]:
        """
        Complete Phase 1A theoretical gateway validation.
        
        Returns:
            {
                'observability': {...},
                'identifiability': {...},  
                'gateway_passed': bool,
                'recommendations': List[str]
            }
        """
        
    def observability_analysis(self) -> Dict[str, Any]:
        """Kalman rank condition analysis on symbolic matrices."""
        
    def identifiability_analysis(self) -> Dict[str, Any]:
        """Transfer function coefficient matching analysis."""
        
    def parameter_constraints(self) -> List[str]:
        """Physical parameter constraint equations."""
```

### Step 6: Create `src/phynance/identification/pem_identifier.py`

**Goal:** Implement PEM (Prediction Error Minimization) with Kalman filter - the numerical validation stage.

**Location:** `src/phynance/identification/pem_identifier.py` (new file)

**What to implement:**

```python
class PEMIdentifier:
    """
    PEM identifier for Phase 1A Linear De-coupling Gateway.
    
    Uses Kalman filter for MLE estimation of θ_agent parameters in the presence
    of bidirectional coupling with Market Maker. Applied only after successful
    gateway analysis.
    """
    
    def __init__(self, coupled_phs_system: PHSBase):
        """Initialize with symbolic coupled PHS system."""
        
    def identify_parameters(self, y_obs: Array, u_ext: Array) -> Dict[str, Any]:
        """Main PEM identification routine."""
        
    def neg_log_likelihood(self, theta_guess: Array, y_obs: Array) -> float:
        """Objective function for PEM optimization."""
        
    def validate_identification(self, theta_true: Dict, theta_identified: Dict) -> Dict:
        """Validate identification results against ground truth."""
```

### Step 7: Update `coupled_linear.py` Implementation

**Goal:** Clean implementation using the new symbolic foundation.

**Location:** `src/phynance/synthetic/coupled_linear.py`

**Implementation approach:**

```python
from ..core import SymbolicQuadraticHamiltonian, SymbolicDissipationMatrix
from ..identification import IdentifiabilityAnalyzer, PEMIdentifier

class CoupledLinearPHS(PHSBase):
    """
    White-box coupled agent-Market Maker system for Phase 1A Linear De-coupling Gateway.
    
    Both agent and Market Maker are PHS subsystems with bidirectional coupling,
    creating the most challenging identifiability scenario.
    """
    
    def __init__(self, agent_dim: int = 1, market_dim: int = 1):
        # Define state and parameter names for coupled system
        state_names = ['q_agent', 'p_agent', 'q_market', 'p_market']
        param_names = ['m_agent', 'k_agent', 'm_market', 'k_market', 
                      'r_agent', 'r_market', 'r_coupling']
        
        # Initialize symbolic components
        self.hamiltonian = SymbolicQuadraticHamiltonian(state_names, param_names)
        self.dissipation = SymbolicDissipationMatrix(state_names, param_names)
        
        # Initialize analysis tools
        self.analyzer = IdentifiabilityAnalyzer(self)
        self.identifier = PEMIdentifier(self)
        
    def gateway_validation(self) -> Dict[str, Any]:
        """Complete Phase 1A gateway validation pipeline."""
        # Step 1: Theoretical validation
        analysis_results = self.analyzer.gateway_analysis()
        
        if not analysis_results['gateway_passed']:
            return analysis_results
            
        # Step 2: Numerical validation (if theoretical validation passes)
        return analysis_results
        
    def run_pem_identification(self, y_obs: Array, u_ext: Array, 
                              theta_true: Dict) -> Dict[str, Any]:
        """Run PEM identification and validate against ground truth."""
        return self.identifier.identify_parameters(y_obs, u_ext)
```

## Development Flow

### Phase 1: Core Symbolic Foundation
1. Implement `SymbolicQuadraticHamiltonian`
2. Implement `SymbolicDissipationMatrix`  
3. Create `symbolic_utils.py` functions
4. Extend `PHSBase` with symbolic methods

### Phase 2: Identifiability Analysis
1. Implement `IdentifiabilityAnalyzer`
2. Test observability analysis on simple examples
3. Test identifiability analysis with known cases

### Phase 3: Integration
1. Update `CoupledLinearPHS` to use symbolic foundation
2. Test complete gateway analysis pipeline
3. Validate against Phase 1A requirements

### Phase 4: Numerical Pipeline
1. Implement PEM optimizer using symbolic->numerical conversion
2. Add Kalman filter with symbolic state matrices
3. Complete synthetic data generation and validation

## Testing Strategy

### Unit Tests (`tests/test_core/`)
- Test symbolic matrix construction
- Test observability rank computation
- Test transfer function derivation
- Test parameter substitution

### Integration Tests (`tests/test_identification/`)
- Test complete gateway analysis on known systems
- Test PEM parameter recovery on synthetic data
- Test convergence from multiple initial conditions

### Validation Tests
- Compare with manual symbolic computations
- Verify against textbook examples
- Cross-check observability vs identifiability results

## Key Implementation Notes

1. **JAX/Diffrax Integration:** Use Diffrax for ODE-SDE integration with OU process excitation.

2. **Coupled System Focus:** Single 4x4 PHS system with agent-market cross-coupling terms.

3. **Symbolic + Numerical:** SymPy for analysis, JAX for computation, seamless conversion.

4. **Diffrax Compatibility:** All dynamics methods support both direct calls and Diffrax integration.

5. **Phase 1A Specific:** Foundation designed specifically for linear coupled system identification.

This foundation provides the symbolic and numerical capabilities needed for Phase 1A gateway validation.
# Phase 1A Implementation Guide: Linear De-coupling Gateway

This document combines the implementation plan, theoretical conditions, and initial steps for Phase 1A of the Phynance project - the critical "Linear De-coupling Gateway" that validates the fundamental inverse problem solvability.

## Overview

Phase 1A serves as the foundational mathematical gateway that must be passed before proceeding to Phase 1B's nonlinear discovery pipeline. The objective is to formally prove that agent parameters `θ_agent` can be uniquely recovered even in the most challenging scenario: a "white-box" coupled system where bidirectional feedback exists between the agent and Market Maker subsystems.

**Key Change from Original Plan:** Phase 1A now uses standard system identification methods (PEM with Kalman filter) rather than the nested EM curriculum, which is moved to Phase 1B for nonlinear discovery validation.

## Core Theoretical Requirements

Before implementation, we must satisfy two critical mathematical conditions that form the theoretical foundation of our approach.

### 1. Observability: Can You See the State?

Observability determines whether you can infer the complete internal state vector `z` (including the latent `p_agent`) from the measurements `y` (the observable `q_agent` and `p_market`).

For your LTI system defined by the matrices `A` and `C`, the condition for complete observability is the **Kalman Rank Condition**.

You must construct the **Observability Matrix**, $\mathcal{O}$, as follows:

$$\mathcal{O} = \begin{bmatrix} C \\ CA \\ CA^2 \\ \vdots \\ CA^{n-1} \end{bmatrix}$$

where `n` is the dimension of your state vector `z`.

The system is **completely observable** if and only if this matrix has full column rank.
$$\text{rank}(\mathcal{O}) = n$$

**Implementation:** Symbolically construct the `A` and `C` matrices from PHS parameters (`m`, `k`, `R`, etc.). Form the observability matrix $\mathcal{O}$ and prove that its rank equals the number of states in the coupled system. If the rank is less than `n`, some part of the system's state (e.g., a combination of internal momentum and market state) is "hidden" from the chosen outputs, requiring changes to the measurement model (`C`) or system parameterization.

### 2. Structural Identifiability: Can You Find the Parameters?

Identifiability is a more demanding condition. It asks: "Assuming the system is observable, can I uniquely determine the underlying physical parameter vector $\theta = \{m_{agent}, k_{agent}, \dots\}$ from the input-output data?"

Observability is a prerequisite for identifiability, but it is not sufficient.

The most direct method, as you alluded to in your plan, is based on the system's **transfer function**. The state-space model can be converted into an input-output transfer function, `G(s)`. For a deterministic system with input `u`, this would be $G(s) = C(sI - A)^{-1}B$. For your stochastic system, we analyze the transfer function that shapes the noise, or equivalently, the power spectral density of the output.

The condition for **global structural identifiability** is:
The mapping from the parameter vector `θ` to the transfer function `G(s, θ)` must be **one-to-one**.

In other words, two different parameter sets, `θ₁` and `θ₂`, cannot produce the exact same transfer function.
$$G(s, \theta_1) = G(s, \theta_2) \quad \iff \quad \theta_1 = \theta_2$$

**Practical Steps for Proof:**
1. **Symbolic Derivation:** Derive the symbolic transfer function `G(s, θ)` from the `A(θ)` and `C(θ)` matrices.
2. **Coefficient Matching:** The coefficients of the numerator and denominator polynomials of `G(s)` will be expressions involving the physical parameters `θ`.
3. **Solve for Parameters:** Show that a system of algebraic equations can be formed from these coefficients and that this system has a **unique solution** for the parameter vector `θ`.

**Note:** The observability rank test is often the first and most practical check for identifiability issues. If the symbolic observability matrix is rank-deficient, the parameters governing the unobservable states are immediately proven to be unidentifiable, reinforcing the strong link between observability and identifiability.

If there are more parameters in `θ` than independent coefficients in the transfer function, or if different combinations of parameters result in the same coefficients, the system is **structurally unidentifiable**. This would mean, for example, that the effect of the agent's mass (`m_agent`) cannot be distinguished from one of the market friction parameters in `R_market` using only the observed data.

Successfully proving both observability and identifiability for the linearized PHS model provides the mathematical confidence required to proceed to the more complex non-linear discovery in Phase 2.

## Implementation Plan

### Step 0: White-Box Coupled Agent-Market Maker System Definition

**Goal:** To fully define the "white-box" coupled system where both agent and Market Maker are PHS subsystems with known laws and bidirectional coupling.

**System Architecture:**
```
z_total = [q_agent, p_agent, q_market, p_market]^T

θ_agent = {m_agent, k_agent, r_agent, ...}    # Agent subsystem parameters
θ_market = {m_market, k_market, r_market, ...} # Market Maker subsystem parameters  
θ_coupling = {r_coupling, ...}                # Bidirectional coupling parameters
```

**Key Insight:** This creates the most challenging identifiability scenario because the agent affects the Market Maker and vice versa. Success here validates that the core inverse problem is solvable.

1. **Define Total State Vector:** `z_total = [q_agent, p_agent, q_market, p_market]^T`
2. **Define Parameter Sets:** Separate agent, Market Maker, and coupling parameters
3. **Construct Coupled PHS Matrices:** Build matrices for the total coupled system:
   * **Hamiltonian Matrix (`M`):** The matrix for the quadratic Hamiltonian `H(z) = 0.5 * z^T * M(θ) * z`.
   * **Structure Matrix (`J_total`):** The constant canonical symplectic matrix.
   * **Dissipation Matrix (`R_total(θ)`):** The matrix containing all friction and coupling dissipation terms.

### Step 1: Stochastic PHS Formulation with Market Maker Price Perturbation

**Goal:** Properly formulate the stochastic PHS where external order flow directly perturbs the Market Maker's price state.

**Stochastic PHS System:**
```
# State vector: z_total = [q_agent, p_agent, q_mm, p_mm]^T
# Process noise enters ONLY the Market Maker's price (p_mm)

dz_total = A_total(θ) z_total dt + E dW(t)

where:
A_total(θ) = (J_total - R_total(θ)) ∇²H_total(θ)  # Deterministic PHS dynamics
E = [0, 0, 0, σ_flow]^T                           # Noise mapping matrix
```

**Economic Interpretation:**
- `dW(t)`: External order flow (everyone else's trading)
- `σ_flow dW(t)`: Direct impact on Market Maker's price (`p_mm`)
- Agent observes price changes and reacts through deterministic coupling
- Creates realistic causal chain: External Flow → Market Price → Agent Response

**Two-Stage Analysis Approach:**
1. **LTI State-Space Analysis:** Convert to `dx = Ax dt + E dW` for symbolic observability/identifiability analysis
2. **Stochastic PHS Simulation:** Use native PHS form with Diffrax for PEM identification

### Step 2: Symbolic Gateway Analysis (Prerequisite for PEM)

**Critical Requirement:** Theoretical validation MUST pass before attempting PEM identification.

**Analysis on LTI Form:**
```
# Convert stochastic PHS to standard LTI form for analysis
dx/dt = A(θ) x + E w(t)    # where x = z_total
y_k = C x(t_k) + v_k       # Partial observation (typically only positions)
```

**Gateway Checks:**

1. **Observability Analysis:**
   - Construct symbolic observability matrix: `O = [C; CA; CA²; ...; CA^(n-1)]`
   - Verify `rank(O) = n` for all admissible parameter values
   - **Challenge:** Market Maker momentum `p_mm` may be unobservable if only `q` states are measured

2. **Structural Identifiability:**
   - Derive transfer function `G(s,θ) = C(sI - A(θ))^(-1)E`
   - Prove parameter-to-coefficient mapping is one-to-one
   - **Focus:** Can `θ_agent` be distinguished from `θ_market` and `θ_coupling`?

3. **Controllability Check:**
   - Ensure noise injection `E` can reach all system modes
   - Verify `rank([E, AE, A²E, ...]) = n`

### Step 3: Stochastic PHS Simulation for Data Generation

**Goal:** Generate synthetic data using the native stochastic PHS formulation for PEM identification.

**Simulation Setup:**
1. **True Parameters:** Define `θ_agent_true`, `θ_market_true`, `θ_coupling_true`, `σ_flow`
2. **Initial Conditions:** Set physically meaningful `z_total(0) = [q_agent(0), p_agent(0), q_mm(0), p_mm(0)]^T`
3. **Stochastic Integration:** Use Diffrax to solve the SDE:
   ```
   dz_total = (J_total - R_total(θ_true)) ∇H_total(z_total) dt + E dW(t)
   ```

**Key Implementation Points:**
- **Native PHS Form:** Simulate in PHS coordinates, not LTI state-space
- **Market Maker Price Noise:** Only `p_mm` receives direct stochastic kicks
- **Observation Model:** `y_k = C z_total(t_k) + v_k` (typically observe only positions)
- **Data Richness:** `σ_flow` must be large enough to provide persistent excitation

**Output Data:**
- Ground truth trajectory: `z_total_true(t)`  
- Noisy observations: `y_obs = [y_1, y_2, ..., y_T]`

### Step 4: PEM Implementation (MLE via Kalman Filter on Stochastic PHS)

**Goal:** Implement PEM identification directly on the stochastic PHS model, not converted LTI form.

**PEM Architecture:**
```python
def neg_log_likelihood(θ_guess, y_obs):
    # 1. Build stochastic PHS matrices from θ_guess
    A_phs = (J_total - R_total(θ_guess)) @ M_total(θ_guess)
    E_phs = [0, 0, 0, σ_flow]  # Market Maker price noise only
    
    # 2. Run Kalman filter on PHS state-space
    # (This maintains PHS structure while enabling MLE)
    
    # 3. Compute negative log-likelihood from innovations
    return total_nll
```

**Key Implementation Details:**

1. **Stochastic PHS Kalman Filter:**
   - State transition: `z_{k+1} = z_k + A_phs(θ) z_k Δt + E_phs √Δt w_k`
   - Observation: `y_k = C z_k + v_k`
   - **Critical:** Preserve PHS structure throughout identification

2. **Parameter Estimation:**
   - **Target:** Focus on recovering `θ_agent` parameters
   - **Constraints:** Maintain PHS physical constraints (R ⪰ 0, etc.)
   - **Optimization:** Use `optimistix.minimise` with multiple random starts

3. **Validation Strategy:**
   - **Parameter Recovery:** Compare `θ_agent_identified` vs `θ_agent_true`
   - **Innovation Diagnostics:** Check whiteness of Kalman innovations
   - **Multiple Starts:** Verify consistent convergence across initializations

### Step 5: Gateway Validation Results

**Success Criteria for Phase 1A:**

1. **Theoretical Gateway (Prerequisites):**
   - ✅ Observability matrix has full rank
   - ✅ Transfer function coefficients uniquely determine `θ_agent`
   - ✅ Controllability ensures noise excites all modes

2. **Numerical Gateway (PEM Validation):**
   - ✅ **Parameter Recovery:** `||θ_agent_identified - θ_agent_true|| < tolerance`
   - ✅ **Statistical Quality:** Kalman innovations pass whiteness tests
   - ✅ **Robustness:** Consistent results across multiple optimization starts
   - ✅ **Physical Consistency:** All identified parameters satisfy PHS constraints

3. **Economic Interpretation:**
   - Agent parameters are identifiable even with bidirectional Market Maker coupling
   - External order flow noise propagates correctly through the system
   - Coupling strength parameters are distinguishable from agent dynamics

**Gateway Decision:** Only proceed to Phase 1B if ALL criteria are satisfied.

## Final Phase 1A Success Criteria

**This is the critical go/no-go checkpoint before Phase 1B nonlinear discovery.**

**Mathematical Foundation:**
1. **Observability Proof:** Symbolic rank analysis confirms all states are observable
2. **Identifiability Proof:** Transfer function analysis confirms `θ_agent` parameters are uniquely recoverable
3. **Controllability Proof:** External order flow noise provides sufficient system excitation

**Numerical Validation:**
4. **Parameter Recovery:** PEM successfully recovers `θ_agent_true` with statistical confidence
5. **Coupling Resilience:** Identification succeeds despite bidirectional agent-Market Maker coupling
6. **Stochastic Consistency:** Market Maker price noise propagation is correctly modeled

**Physical Realism:**
7. **PHS Constraints:** All parameters satisfy physical realizability (R ⪰ 0, bounded H, etc.)
8. **Economic Coherence:** Identified parameters have interpretable economic meaning

**Phase 1A validates the fundamental premise:** *Agent parameters can be recovered even in the most challenging coupled scenario.*

✅ **Gateway Passed** → Proceed to Phase 1B nonlinear discovery validation  
❌ **Gateway Failed** → Revisit modeling assumptions or identification methodology

## Implementation Notes

**Technology Stack:**
- **Symbolic Analysis:** SymPy for observability/identifiability proofs
- **Stochastic Simulation:** JAX/Diffrax for native stochastic PHS integration
- **PEM Optimization:** Optimistix for constrained parameter identification
- **Structure:** Native PHS formulation throughout (no LTI conversion for numerics)

**Key Design Decisions:**
- **Market Maker Price Noise:** External order flow enters only through `p_mm` state
- **Coupled System Focus:** Single 4×4 PHS with bidirectional agent-Market Maker coupling
- **Two-Stage Analysis:** Symbolic gateway analysis → Numerical PEM validation
- **Configuration Driven:** All experiments parameterized through YAML files

**Critical Success Factor:** Maintaining PHS structure and economic interpretation throughout the entire identification pipeline.
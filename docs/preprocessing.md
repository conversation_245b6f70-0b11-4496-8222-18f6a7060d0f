# Data Preprocessing Pipeline for PHS System Identification

## Objective

Transform raw, discrete, and noisy financial time-series data into clean, continuous-time representations of the system state $z(t) = [q(t), p(t)]^T$ and its time derivative $\dot{z}(t) = [\dot{q}(t), \dot{p}(t)]^T$ suitable for Port-Hamiltonian System identification.

## The Core Challenge: Derivative Estimation from Noisy Data

### Problem Statement
- **Observable**: Discrete, noisy state measurements $z_k = z(t_k) + \epsilon_k$
- **Required**: Clean estimates of both $z(t)$ and $\dot{z}(t)$ for PHS identification
- **Challenge**: Standard numerical differentiation amplifies noise catastrophically

Financial data presents unique challenges:
- **Market microstructure noise**: Bid-ask spreads, discrete tick sizes
- **Non-uniform sampling**: Trading hours, holidays, varying liquidity
- **Regime changes**: Volatility clustering, structural breaks
- **Cross-asset dependencies**: Correlated rebalancing across holdings

### Why Standard Approaches Fail
Simple finite differences $\frac{z_{k+1} - z_k}{\Delta t}$ amplify noise by factor $O(1/\Delta t)$, making derivatives unusable for system identification.

## Proposed Methodology: Multi-Stage Pipeline

### Stage 1: Data Cleaning & Preparation

**Input**: Raw time series $\{t_k, q_k, p_k\}$ for $k = 1, \ldots, N$

**Operations**:
1. **Missing Value Handling**
   - Holdings ($q$): Forward-fill for temporary missing data
   - Prices ($p$): Linear interpolation for short gaps, exclude assets with extensive missing data
   - Exclude periods with excessive missing data across multiple assets

2. **Timestamp Alignment**
   - Establish common time grid (daily frequency)
   - Handle market closures and holidays consistently
   - Ensure synchronized observations across all state variables

3. **Outlier Detection**
   - Identify and flag extreme values using robust statistical methods
   - Consider market events (earnings, splits, extraordinary transactions)
   - Apply domain knowledge to distinguish outliers from genuine market events

4. **Stationarity Preprocessing**
   - Test for unit roots in price series
   - Apply appropriate transformations (differencing, detrending)
   - Handle structural breaks through regime detection

### Stage 2: Smoothing & Continuous Representation

**Objective**: Fit continuous analytical functions to discrete, noisy observations

#### Primary Method: Smoothing Splines

**Mathematical Formulation**:
For each state variable $x_i(t)$, solve:
$$\min_{f} \sum_{k=1}^N (x_{i,k} - f(t_k))^2 + \lambda \int (f''(t))^2 dt$$

where:
- First term: Data fidelity
- Second term: Smoothness penalty (penalizes curvature)
- $\lambda$: Smoothing parameter (controls bias-variance tradeoff)

**Advantages**:
- Produces continuous analytical function $f(t)$
- Derivatives $f'(t)$ calculated analytically (no numerical instability)
- Well-established statistical theory for parameter selection
- Natural uncertainty quantification

**Implementation**:
```python
from scipy.interpolate import UnivariateSpline

def fit_smoothing_spline(t, x, smoothing_factor=None):
    """
    Fit smoothing spline to time series data
    
    Parameters:
    - t: time points
    - x: observations
    - smoothing_factor: if None, determined by cross-validation
    """
    spline = UnivariateSpline(t, x, s=smoothing_factor)
    return spline

def get_analytical_derivative(spline, t):
    """Calculate analytical derivative from fitted spline"""
    return spline.derivative()(t)
```

#### Alternative Method: Savitzky-Golay Filter

**Purpose**: Benchmark comparison and computational efficiency

**Approach**:
- Fit local polynomials to sliding windows
- Provides both smoothed values and derivatives
- Computationally efficient for large datasets

#### Multivariate Considerations

**Cross-Asset Dependencies**:
For ETF holdings, rebalancing affects multiple assets simultaneously. Consider:

1. **Independent Smoothing**: Smooth each $q_i(t)$ and $p_i(t)$ separately
   - Pros: Computationally simple, well-understood
   - Cons: May break economic relationships

2. **Constrained Smoothing**: Enforce economic constraints
   - Total value constraint: $\sum_i q_i(t) p_i(t) = \text{constant}$ (approximately)
   - Correlation preservation: Maintain cross-asset correlation structure

3. **Tensor Product Splines**: Multivariate smoothing
   - Pros: Preserves cross-dependencies naturally
   - Cons: Computational complexity, curse of dimensionality

**Planned Approach**: Start with independent smoothing for Phase 1 validation, explore constrained methods for Phase 2 if cross-dependencies prove critical.

### Stage 3: Hyperparameter Selection

**Cross-Validation Strategy**:
1. **Time Series CV**: Use expanding window or blocked CV (not random splits)
2. **Metrics**: Balance smoothness vs. fidelity
   - Primary: Prediction error on held-out data
   - Secondary: Economic plausibility (volume consistency, no-arbitrage bounds)

**Smoothing Parameter Selection**:
- **Too small $\lambda$**: Overfitting, noisy derivatives
- **Too large $\lambda$**: Oversmoothing, loss of signal
- **Optimal**: Minimize generalized cross-validation (GCV) score

### Stage 4: Derivative Calculation & Validation

**Analytical Derivatives**:
From fitted splines $f_q(t)$ and $f_p(t)$:
$$\dot{q}(t) = f_q'(t), \quad \dot{p}(t) = f_p'(t)$$

**Quality Assessment**:
1. **Statistical Validation**
   - Compare estimated vs. finite-difference derivatives (visual inspection)
   - Check derivative smoothness and continuity
   - Analyze residuals from spline fits

2. **Economic Validation**
   - **Volume Consistency**: Do estimated $\dot{q}$ align with observed trading volumes?
   - **No-Arbitrage Bounds**: Are price dynamics economically reasonable?
   - **Liquidity Constraints**: Are implied trading rates feasible given market conditions?

3. **Phase 1 Synthetic Validation**
   - Generate synthetic $z(t)$ from known PHS model
   - Add realistic noise: $z_k = z(t_k) + \epsilon_k$
   - Apply preprocessing pipeline
   - Compare estimated $\dot{z}_{est}$ with true $\dot{z}_{true}$
   - Quantify accuracy: RMSE, correlation, phase lag

### Stage 5: Normalization & Final Preparation

**Standardization**:
- Center: $\tilde{z} = z - \bar{z}$
- Scale: $\hat{z} = \tilde{z} / \sigma_z$
- Apply same transformation to derivatives

**Output**: Clean time series $\{t_k, z_k, \dot{z}_k\}$ ready for system identification

## Implementation Strategy

### Modular Architecture
```
src/econe_phs_etf/data_handling/
├── etf_data_loader.py          # Raw data ingestion
├── etf_preprocessor.py         # Main preprocessing pipeline
├── smoothing_methods.py        # Spline fitting, Savitzky-Golay
├── validation_tools.py         # Economic & statistical validation
└── derivative_approximators.py # Alternative derivative methods
```

### Key Classes
```python
class ETFPreprocessor:
    def __init__(self, method='spline', validation_mode=False):
        self.method = method
        self.validation_mode = validation_mode
    
    def clean_raw_data(self, raw_data):
        """Stage 1: Data cleaning"""
        
    def fit_smooth_representation(self, clean_data):
        """Stage 2: Smoothing splines"""
        
    def estimate_derivatives(self, smooth_repr):
        """Stage 4: Analytical derivatives"""
        
    def validate_results(self, z, z_dot):
        """Economic and statistical validation"""
        
    def normalize_data(self, z, z_dot):
        """Stage 5: Final preprocessing"""
```

## Validation Protocol

### Phase 1: Synthetic Data Validation
1. **Generate ground truth**: Simulate PHS model with known parameters
2. **Add realistic noise**: Multiple noise models (white, GARCH, microstructure)
3. **Apply pipeline**: Full preprocessing on noisy synthetic data
4. **Quantify performance**: Derivative estimation accuracy vs. noise levels
5. **Sensitivity analysis**: Robustness to different market conditions

### Phase 2: Real Data Validation
1. **Economic plausibility**: Volume consistency, arbitrage bounds
2. **Cross-validation**: Out-of-sample prediction performance
3. **Regime robustness**: Performance across different market periods
4. **Sensitivity testing**: Stability to hyperparameter choices

## Expected Challenges & Mitigation

### Challenge 1: Regime Changes
**Problem**: Structural breaks invalidate global smoothing assumptions
**Mitigation**: 
- Changepoint detection algorithms
- Locally adaptive smoothing with time-varying parameters
- Robust spline methods (Huber loss)

### Challenge 2: Market Microstructure
**Problem**: Discrete price changes, bid-ask effects
**Mitigation**:
- Volume-weighted smoothing
- Microstructure-aware noise models
- Tick-by-tick data preprocessing when available

### Challenge 3: Computational Complexity
**Problem**: Large datasets, multivariate smoothing
**Mitigation**:
- Efficient spline algorithms (sparse matrices)
- Parallel processing for multiple assets
- Hierarchical smoothing (factor models)

### Challenge 4: Economic Constraints
**Problem**: Smoothing may violate economic relationships
**Mitigation**:
- Constrained optimization formulations
- Post-processing constraint enforcement
- Economic penalty terms in smoothing objective

## Success Metrics

### Statistical Metrics
- **Derivative Accuracy**: RMSE($\dot{z}_{est}$, $\dot{z}_{true}$) in Phase 1
- **Signal Preservation**: Correlation between smoothed and true signals
- **Noise Reduction**: Signal-to-noise ratio improvement

### Economic Metrics
- **Volume Consistency**: Correlation($|\dot{q}|$, trading volume)
- **Price Bounds**: Percentage of derivatives satisfying no-arbitrage constraints
- **Liquidity Feasibility**: Trading rate vs. market capacity analysis

### System Identification Metrics
- **Parameter Recovery**: Accuracy of PHS identification with preprocessed data
- **Model Fit**: Goodness-of-fit of identified PHS to out-of-sample data
- **Stability**: Robustness of identification across different preprocessing parameters

This preprocessing pipeline is critical for the success of the entire PHS identification framework, as the quality of the derivatives directly determines the reliability of the learned Hamiltonian, dissipation, and input mappings.
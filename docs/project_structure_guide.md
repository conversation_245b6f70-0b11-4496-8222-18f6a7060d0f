# Phynance Project Structure Guide: The Structure of Strategy

## Overview

This guide explains the complete project structure and how all components interact to implement the two-phase validation methodology for discovering the dynamics of active ETFs using Port-Hamiltonian Systems.

## Directory Structure & Purpose

```
phynance/
├── src/phynance/               # Core implementation (main source base)
├── config/                     # Configuration management
├── data/                       # Data storage and organization
├── models/                     # Saved model artifacts
├── notebooks/                  # Interactive development and analysis
├── scripts/                    # Execution pipelines
├── results/                    # Analysis outputs
├── tests/                      # Testing infrastructure
├── scratchpad/                 # Experimental/temporary code
└── docs/                       # Documentation
```

## Core Source Directory (`src/phynance/`)

The source directory implements a **DRY (Don't Repeat Yourself) architecture** with **progressive complexity**, where components are systematically reused across the three phases (1A → 1B → 2).

### **Core Module (`core/`)**
**Purpose**: Fundamental PHS components shared across ALL phases
**Key Components**:

- `phs_base.py`: Abstract base class defining PHS dynamics equation `dz/dt = (J-R)∇H + gu`
- `state_variables.py`: StateVariables class with economic interpretation (q=holdings, p=internal prices)
- `hamiltonian_base.py`: Abstract Hamiltonian H(q,p) with QuadraticHamiltonian, PolynomialHamiltonian implementations
- `dissipation_base.py`: Dissipation matrix R(q,p) ensuring positive semi-definite constraint
- `coupling_base.py`: Coupling matrix g(q,p) for external forces
- `ode_integrators.py`: JAX/Diffrax integration utilities

**Interaction Pattern**: Every other module inherits from or uses these base classes.

### **Synthetic Module (`synthetic/`)**
**Purpose**: Phase 1 validation systems with known ground truth
**Key Components**:

- `coupled_linear.py`: Phase 1A linear agent-market coupled system for identifiability analysis
- `nonlinear_agent.py`: Phase 1B nonlinear synthetic system with known H, R, g for EM validation
- `ground_truth_generator.py`: Creates synthetic datasets with controlled parameters

**Dependencies**: Uses `core/` base classes, configured by `config/phase1a_*.yml` and `config/phase1b_*.yml`

### **Identification Module (`identification/`)**
**Purpose**: System identification algorithms shared across phases
**Key Components**:

- `pem_optimizer.py`: Prediction Error Method for Phase 1A linear parameter identification
- `bootstrap_strategies.py`: Multiple initialization methods for latent momentum p₀ (used in 1B, 2)
- `ekf_state_estimator.py`: Extended Kalman Filter for latent state inference (used in 1B, 2)
- `em_coordinator.py`: EM algorithm orchestrator managing iterative discovery loop (used in 1B, 2)
- `identifiability_analyzer.py`: Formal structural identifiability analysis (Phase 1A)
- `convergence_monitor.py`: Stopping criteria and convergence diagnostics

**Reuse Pattern**:
- Phase 1A: Uses PEM + identifiability analyzer
- Phase 1B: Adds bootstrap + EKF + EM coordinator  
- Phase 2: Reuses all Phase 1B components

### **Learning Module (`learning/`)**
**Purpose**: Machine learning components for physics discovery
**Structure**:

```
learning/
├── hnn/                        # Hamiltonian Neural Networks
│   ├── architecture.py         # JAX/Flax HNN model definitions
│   ├── training.py             # Conservation-constrained training
│   └── validation.py           # Energy conservation checks
└── symbolic/                   # Symbolic Regression
    ├── pysr_wrapper.py         # PySR interface for R(q,p), g(q,p) discovery
    ├── expression_validator.py # Ensures R positive semi-definite
    └── economic_constraints.py # Domain knowledge constraints
```

**Dependencies**: Uses `core/` base classes, coordinated by `identification/em_coordinator.py`
**Used by**: Phase 1B (validation) and Phase 2 (real discovery)

### **Market Module (`market/`)**
**Purpose**: Phase 2 market environment characterization
**Key Components**:

- `dfsv_bif.py`: Integration with DFSV-BIF from previous thesis for factor extraction
- `factor_extractor.py`: Extract latent factors f_t and log-volatilities h_t
- `market_state_builder.py`: Construct market state z_market = [f_t, h_t] for external inputs

**Dependencies**: Only used in Phase 2, provides inputs to `identification/em_coordinator.py`

### **Data Module (`data/`)**
**Purpose**: Data handling utilities shared across phases
**Key Components**:

- `etf_loader.py`: ARKK holdings and price data acquisition
- `derivative_estimator.py`: Smoothing splines for dq/dt, dp/dt from noisy data
- `data_validator.py`: Economic plausibility checks and outlier detection
- `preprocessor.py`: Data cleaning, alignment, normalization

**Used by**: All phases for different data sources (synthetic vs. real)

### **Analysis Module (`analysis/`)**
**Purpose**: Results interpretation and economic insights
**Key Components**:

- `hamiltonian_interpreter.py`: H(q,p) landscape visualization and economic meaning
- `friction_analyzer.py`: R(q,p) cost attribution and breakdown
- `coupling_analyzer.py`: g(q,p) sensitivity analysis to market forces
- `trajectory_analyzer.py`: p_agent evolution and strategic insights
- `performance_metrics.py`: Model validation scores
- `economic_insights.py`: Bridge between discovered laws and Economic Engineering theory

**Used by**: All phases for validation and interpretation, primary consumer of results

## Supporting Directories

### **Configuration System (`config/`)**
**Purpose**: Centralized parameter management with inheritance

```
config/
├── shared_defaults.yml         # Common parameters (integration, optimization, logging)
├── phase1a_linear_coupled.yml  # Phase 1A specific (system params, identifiability settings)
├── phase1b_nonlinear_synthetic.yml # Phase 1B specific (EM settings, HNN/PySR configs)
└── phase2_arkk_real.yml        # Phase 2 specific (data sources, market engine)
```

**Interaction**: Scripts load configs, pass to src modules. Phase-specific configs inherit from shared_defaults.yml.

### **Data Organization (`data/`)**
**Purpose**: Structured data storage matching pipeline flow

```
data/
├── raw/                        # Original datasets
│   ├── market_factors/         # DFSV-BIF market data
│   └── arkk_holdings/          # ETF holdings and prices
├── synthetic/                  # Phase 1 generated data
│   ├── phase1a_coupled_linear/ # Agent-market coupling data
│   └── phase1b_nonlinear/      # Known ground truth trajectories
├── processed/                  # Clean, aligned data
│   ├── market_state/           # z_market = [f_t, h_t]
│   └── agent_observations/     # q_obs, derivatives
└── validation/                 # Ground truth for validation phases
```

**Flow**: `raw/` → `data/` module processing → `processed/` → `src/` analysis → `results/`

### **Model Artifacts (`models/`)**
**Purpose**: Persist trained/discovered components

```
models/
├── phase1a_validated/          # Linear PHS parameters, identifiability results
├── phase1b_validated/          # HNN weights, symbolic equations, EM convergence
└── phase2_discovered/          # Final ARKK model: H, R, g discovered laws
```

**Interaction**: `src/` modules save/load model artifacts, `scripts/` coordinate persistence

### **Execution Pipelines (`scripts/`)**
**Purpose**: High-level orchestration of phases

- `run_phase1a_gateway.py`: Coordinates `synthetic/coupled_linear.py` + `identification/pem_optimizer.py`
- `run_phase1b_validation.py`: Coordinates full EM loop with `learning/` + `identification/em_coordinator.py`
- `run_phase2_discovery.py`: Coordinates real data pipeline with `market/` + all validated components
- `generate_thesis_figures.py`: Uses `analysis/` module to create publication figures

**Pattern**: Scripts are thin orchestration layer, actual logic in `src/` modules

### **Interactive Development (`notebooks/`)**
**Purpose**: Exploratory analysis and development

- `01_phase1a_gateway_validation.ipynb`: Interactive development of identifiability analysis
- `02_phase1b_em_algorithm_validation.ipynb`: EM loop debugging and convergence analysis
- Additional notebooks for Phase 2 development and results interpretation

**Relationship**: Notebooks import from `src/` modules, use same configs as scripts, save outputs to `results/`

### **Results Organization (`results/`)**
**Purpose**: Structured output storage

```
results/
├── phase1a_identifiability/    # Formal analysis results, parameter recovery
├── phase1b_em_validation/      # Ground truth recovery, convergence diagnostics
├── phase2_discovery/           # Discovered laws, ARKK insights
└── comparative_analysis/       # Cross-phase validation, benchmarking
```

**Sources**: Generated by `analysis/` module, organized by `scripts/`, visualized in `notebooks/`

### **Development Support**

- **`tests/`**: Unit tests for each `src/` module, integration tests for phase pipelines
- **`scratchpad/`**: Experimental code, quick prototypes, temporary explorations
- **`docs/`**: Theoretical documentation, implementation guides, thesis materials

## Component Interaction Flow

### **Phase 1A Flow**:
1. `config/phase1a_*.yml` → `scripts/run_phase1a_gateway.py`
2. `synthetic/coupled_linear.py` creates system using `core/` base classes
3. `identification/pem_optimizer.py` + `identifiability_analyzer.py` analyze system
4. Results saved to `models/phase1a_validated/` and `results/phase1a_identifiability/`

### **Phase 1B Flow**:
1. `config/phase1b_*.yml` → `scripts/run_phase1b_validation.py`  
2. `synthetic/nonlinear_agent.py` creates known ground truth system
3. `identification/bootstrap_strategies.py` initializes latent states
4. `identification/em_coordinator.py` orchestrates iterative loop:
   - M-step: `learning/hnn/` + `learning/symbolic/` update physics
   - E-step: `identification/ekf_state_estimator.py` updates states
5. Ground truth comparison validates methodology

### **Phase 2 Flow**:
1. `config/phase2_*.yml` → `scripts/run_phase2_discovery.py`
2. `market/dfsv_bif.py` characterizes market environment
3. `data/` module preprocesses ARKK data  
4. Same EM loop as Phase 1B but on real data
5. `analysis/` module interprets discovered laws for economic insights

## Key Design Principles

### **Progressive Complexity**
Each phase builds on the previous, reusing validated components:
- 1A validates basic identification capability
- 1B validates complete discovery pipeline  
- 2 applies validated pipeline to real problem

### **Configuration-Driven**
All experiments controlled through YAML configs enabling:
- Reproducible research
- Easy parameter sweeps
- Clean separation of implementation and experimentation

### **Separation of Concerns**
- `core/`: Mathematical abstractions
- `synthetic/`: Ground truth systems
- `identification/`: Algorithms  
- `learning/`: ML components
- `market/`: Domain-specific data
- `analysis/`: Interpretation and insights

This structure enables systematic validation of the methodology while maintaining clean interfaces and reusable components throughout the research process.
# Phase 1: Pipeline Development & Validation on Synthetic Data

**Objective:** To prove, in a controlled environment where the ground truth is known, that the proposed identification pipeline can successfully recover latent states and symbolic laws.

This phase is the essential theoretical and practical foundation for the entire thesis. It is divided into two critical sub-phases, each designed to rigorously test a core component of the methodology before applying it to real-world data.

---

## Sub-Phase 1A: Grey-Box PEM for a Linear Coupled Port-Hamiltonian System (The Gateway)

**This sub-phase is a strict go/no-go checkpoint for the project's discovery claim.**

**Objective:** To formally establish the structural identifiability of the core problem—a coupled agent-market system with conservative, dissipative, and interconnection dynamics—from partial and noisy observations.

### Theoretical Foundation

- **Coupled Port-Hamiltonian System:** The agent and market are modeled as two interconnected linear PHS subsystems based on mechanical analogies. The dynamics of the complete system state $z = [z_{agent}^T, z_{market}^T]^T$ are governed by:
  $$\frac{dz}{dt} = (J_{total} - R_{total})\nabla H_{total}(z) + g_{total}u_{ext}(t)$$

- **State Vectors:**
  - Agent State: $z_{agent} = [q_{agent}, p_{agent}]^T$ (holdings as **position**, and internal reservation prices as **momentum**)
  - Market State: $z_{market} = [q_{market}, p_{market}]^T$ (market quantities as **position** and observable market prices as **momentum**)

- **Hamiltonian (Conservative Part):** The total energy is the sum of individual Hamiltonians, analogous to a system of masses and springs:
  - $H_{agent}(q_{agent}, p_{agent}) = \frac{1}{2}k_{agent} (q_{agent} - q_{ref})^2 + \frac{1}{2m_{agent}}p_{agent}^2$ (Potential + Kinetic Energy)
  - $H_{market}(q_{market}, p_{market}) = \frac{1}{2}k_{market} q_{market}^2 + \frac{1}{2m_{market}}p_{market}^2$ (Potential + Kinetic Energy)

- **Dissipation Matrix (Friction Part):** The model includes internal frictions and coupling dissipation, analogous to mechanical damping:
  $$R_{total} = \begin{pmatrix} R_{agent} & R_{coupling} \\ R_{coupling}^T & R_{market} \end{pmatrix}$$
  where $R_{agent}$, $R_{market}$ are diagonal matrices of internal friction parameters (dampers), and $R_{coupling}$ captures dissipative agent-market interactions.

- **Input Coupling Matrix:** External forces affect the system through:
  $$g_{total} = \begin{pmatrix} g_{agent} \\ g_{market} \end{pmatrix}$$

- **Observations:** Only $q_{agent}$ (observable ETF holdings) and $p_{market}$ (observable market prices) are measured: $y = [q_{agent}, p_{market}]^T$ with additive noise.

- **Parameter Set:** The complete set of mechanical analog parameters to be identified:
  $$\theta = \{m_{agent}, k_{agent}, q_{ref}, m_{market}, k_{market}, R_\text{total}, g_{total}\}$$

- **System Identification Challenge:** Learn the complete parameter set $\theta$—including inertial ($m$), stiffness ($k$), **dissipative ($R$)**, and coupling ($g$) parameters—from noisy, partial observations $y$.

### Implementation & Validation

- **Technology Stack:**
  - **JAX**: Automatic differentiation for gradient-based optimization
  - **Optimistix/Equinox**: Robust ODE solvers and optimization algorithms
- **Modular Simulation Architecture:** Design a simulator in Python/JAX for coupled PHS systems
- **Controlled Simulation:** Generate synthetic trajectories from the coupled PHS with known $\theta_{true}$ and realistic noise
- **Preprocessing Pipeline:** Validate smoothing splines methodology for derivative estimation from noisy partial observations
- **Prediction Error Method:** Implement optimization-based parameter identification for the complete coupled PHS
- **Identifiability Analysis:** Conduct formal structural identifiability analysis via transfer function analysis of the coupled LTI state-space form, specifically testing identifiability of friction and coupling parameters

### Critical Outcomes

- Formal proof that $\theta_{identified}$ can uniquely recover $\theta_{true}$ from partial observations
- **Gateway Decision:** If any parameters (especially friction $R$ or coupling $g$) are unidentifiable, the observation scheme is insufficient and the project must be redesigned
- Quantified derivative estimation accuracy from noisy financial data under partial observation
- Understanding of fundamental identification limits for coupled Port-Hamiltonian dynamics
- Benchmark performance metrics for Phase 2

---

## Sub-Phase 1B: Full Iterative Pipeline Validation on a Non-Linear System (The Engine Test)

**Objective:** To validate the complete HNN-PySR-EKF iterative loop's ability to learn a non-linear Hamiltonian and discover known symbolic interaction laws, robustly solving the full latent variable problem. This sub-phase is a critical test of the exact algorithm to be used in Phase 2.

### Theoretical Foundation

- **Model Transition:** The market is no longer modeled as a coupled Hamiltonian system but replaced by an external input port. The synthetic agent PHS is governed by:
  $$\frac{dz}{dt} = (J - R(z))\nabla H(z) + g(z)u(t)$$
  where $z = [q, p]^T$ represents the agent's state (position, momentum) and $u(t)$ is an external input signal (force).

- **Ground Truth Components:**
  - **Agent Hamiltonian:** $H_{agent}(q,p)$ is a known non-linear function (e.g., including non-linear potential/kinetic energy terms: $H = \frac{1}{2}k_1 q^2 + \frac{1}{2m}p^2 + k_2 q^3 + k_3 qp^2$)
  - **Friction Matrix:** $R(q,p)$ represents known symbolic friction laws (e.g., non-linear damping: $R = r_1 |p| + r_2 p^2$)
  - **Coupling Matrix:** $g(q,p)$ defines known symbolic coupling to external forces (e.g., $g = g_0 + g_1 q$)
  - **External Input:** $u(t)$ is a known time-series signal representing market forces

- **Simulation Setup:**
  - Generate the "true" latent state trajectory $z_{true}(t) = [q_{true}(t), p_{true}(t)]$ from the known non-linear PHS
  - Create observable data: $q_{obs}(t) = q_{true}(t) + \epsilon_q$ (holdings/position with noise)
  - The internal price $p_{true}(t)$ (momentum) remains unobservable (latent state problem)

### Implementation & Validation

1. **Data Generation:**
   - Simulate the complete system with known parameters $\theta_{true} = \{H_{params}, R_{params}, g_{params}\}$
   - Generate realistic noise levels for observable quantities
   - Create multiple synthetic datasets with different initial conditions

2. **Bootstrap Strategy Testing:**
   - Test the pipeline's robustness to initialization by comparing different bootstrap methods for the initial momentum guess $p_0$:
     - **Informed Guess (Velocity-based):** $p_0 = m \cdot \dot{q}_{obs}$ (using estimated inertia).
     - **Non-Informative Prior:** A smoothed random walk, to prove robustness.
   - Test each bootstrap method across multiple random seeds to ensure convergence to the same ground truth.

3. **Iterative Discovery Loop Execution:**
   - **Initialization:** Start with bootstrapped $p_0$ and conservative assumption ($R=0, g=0$)
   - **EM Loop:** Execute the exact algorithm from Phase 2:
     - **(M-Step):** Train HNN on current state estimate, discover $R,g$ with PySR
     - **(E-Step):** Update latent state $p$ using EKF with discovered dynamics
     - **Iterate:** Continue until convergence criteria met
   - **Multiple Runs:** Execute from each bootstrap initialization method

4. **Critical Recovery Analysis:**
   - **Hamiltonian Recovery:** Compare learned $H_{discovered}$ vs. $H_{true}$ using function approximation metrics
   - **Symbolic Law Recovery:** Verify PySR discovers the correct functional forms for $R(q,p)$ and $g(q,p)$
   - **State Trajectory Recovery:** Measure $||p_{final} - p_{true}||$ for final inferred momentum
   - **Convergence Consistency:** Ensure all bootstrap methods converge to the same final laws
   - **Robustness Assessment:** Test sensitivity to noise levels and data length

### Implementation Tools

- **JAX/Flax**: Neural network implementation for HNN with conservation constraints
- **Diffrax**: Sophisticated ODE solvers for continuous-time PHS simulation
- **Optax**: Advanced optimization algorithms for neural network training
- **PySR**: Symbolic regression for discovering $R$ and $g$ from residuals
- **JAX**: Extended Kalman Filter implementation with automatic differentiation

### Success Criteria

- **Law Discovery:** PySR must recover the true symbolic expressions for $R$ and $g$ across multiple runs
- **Hamiltonian Learning:** HNN must learn a function that conserves energy and approximates $H_{true}$
- **State Inference:** Final momentum trajectory $p_{final}$ must converge to $p_{true}$ within acceptable error bounds
- **Bootstrap Independence:** Results must be consistent across different initialization methods
- **Noise Robustness:** Performance must degrade gracefully with increasing noise levels

**Critical Outcome:** A definitive, end-to-end validation of the full discovery pipeline, proving its ability to separate conservative from non-conservative dynamics, infer latent states, and discover true functional forms robustly. If convergence is fragile or highly sensitive to initialization, the method requires algorithmic improvements before proceeding to Phase 2.
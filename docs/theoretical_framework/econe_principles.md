# Foundational Principles of Economic Engineering (EconE)

This document outlines the core principles of Economic Engineering (EconE) that form the theoretical basis for this thesis. The methodology is built upon an analogy with classical mechanics, as detailed in the works of <PERSON><PERSON> (2023). This allows for the construction of dynamic, interpretable, and physics-informed models of economic systems.

## 1. The Newtonian Mechanics Analogy

The foundational insight of EconE, as proposed by <PERSON><PERSON> (2023), is to treat economic dynamics in a manner analogous to Newtonian mechanics.

*   **Economic Force (Want/Desire):** The primary driver of economic activity is defined as an economic "force." This is the want or desire for a good or service.
*   **Economic Momentum (Price):** Price ($p$) is not a static value but is defined as the economic analog of momentum. It represents the "value in motion" associated with a trade.
*   **Economic Inertia (Demand):** The resistance to a change in price is analogous to inertia (mass). This is more formally identified with price *inelasticity*.
*   **The Law of Demand (Newton's Second Law):** The core dynamic relationship is $\text{Force} = \frac{d(\text{Price})}{dt}$. This states that an economic force (a "want") is required to cause a change in price (momentum). This provides a causal, dynamic foundation for the law of demand, moving beyond static equilibrium analysis.

## 2. The Port-Hamiltonian Formulation

The Newtonian analogy is formalized using the Port-Hamiltonian Systems (PHS) framework, which is native to mechanics and control theory.

### 2.1. Conjugate State Variables

The critical step is the choice of conjugate variables, which directly enables the Hamiltonian formulation:

*   **Holdings ($q$) as Position:** The quantity of a good held by an agent is the generalized **position**. The rate of change of holdings, $\dot{q}$, is the **velocity**.
*   **Internal Reservation Price ($p$) as Momentum:** The **internal valuation** or **reservation price** an agent ascribes to a good is the generalized **momentum**. The rate of change of this internal price, $\dot{p}$, is the **force** or **want**. This distinction is critical: $p$ is an internal state of the agent, not the observable external market price.

This establishes $q$ (position) and $p$ (momentum) as the fundamental, conjugate state variables of the economic system.

### 2.2. Energy and Surplus

*   **Kinetic Energy:** The energy associated with the flow of trade is analogous to kinetic energy, $T(p) = \frac{1}{2m}p^2$, where 'm' is the market inertia.
*   **Potential Energy:** The energy associated with holding a stock of assets is analogous to potential energy, $V(q) = \frac{1}{2}k(q-q_{ref})^2$, where 'k' is a stiffness parameter representing aversion to deviating from a reference $q_{ref}$.

## 3. Synthesis for Agent-Based PHS Modeling

The combination of these analogies provides the direct theoretical justification for modeling an economic **agent** (e.g., an active ETF) using a Port-Hamiltonian System (PHS). This approach separates the agent's internal dynamics from its interaction with the external market environment.

*   **The Agent Model (First-Principles):** The agent's behavior is governed by a PHS model where the components have direct economic interpretations:
    *   The **Hamiltonian ($H$)** is the agent's total internal **Economic Surplus**, representing its idealized, frictionless objective function. It is a function of the agent's internal state, $H(q,p)$.
    *   The **dissipative matrix ($R$)** models the **economic frictions** (transaction costs, slippage, etc.) that dissipate the agent's surplus during market interaction.
    *   The **input matrix ($g$)** and **external input vector ($u$)** model how the agent responds to external forces, such as its own alpha signals or prevailing market conditions.

*   **The Market Model (Phenomenological):** The external market is treated as a separate, complex environment that provides the inputs ($u$) to the agent model and creates the frictions ($R$) the agent experiences.

This leads to the general PHS formulation for the agent:
$$\frac{dz}{dt} = (J-R)\nabla H + g \cdot u$$

The state-space representation derived from a linear mechanical system is a specific case of this more general, potentially non-linear PHS formulation. This thesis develops a pipeline to identify the non-linear functions for $H$, $R$, and $g$ from observed data.

# PHS Formulation for Active ETFs

This document details the specific application of Economic Engineering (EconE) and Port-Hamiltonian Systems (PHS) to the modeling of active Exchange Traded Funds (ETFs). It translates the general theoretical principles into a concrete mathematical and conceptual framework for this thesis, consistent with the project outline.

## 1. PHS State Variables for an Active ETF

We define the state of the ETF using the conjugate variables justified by the mechanical analogy from EconE and refined for this project's scope:

*   **Holdings ($q$) as Position:** This vector represents the quantity of each asset held by the ETF. It is the economic analog of generalized **position**. For an ETF with $n$ assets, $q$ is a vector in $\mathbb{R}^n$.

*   **Internal Reservation Price ($p$) as Momentum:** This vector represents the ETF's private internal valuation or reservation price for each asset. Following the EconE framework, this agent-specific price is treated as the economic analog of generalized **momentum**. This resolves the conceptual challenge of how an ETF can "own" a price—$p$ is the fund's private valuation that enables a purely internal $H(q,p)$. External market prices are distinct observables that enter as forcing terms in $u(t)$ and influence $\dot{p}$ (the economic **force**) through the equations of motion.

The full state of the system is the concatenated vector $z = [q, p]$.

## 2. PHS Model Components for an Active ETF

The dynamics of the ETF are described by the standard PHS equation, with the canonical skew-symmetric matrix $J$ defined as:

$$\frac{dz}{dt} = (J - R)\nabla H + g \cdot u$$

where $J = \begin{bmatrix} 0 & I \\ -I & 0 \end{bmatrix}$

Here is the specific EconE interpretation for each component in the context of an active ETF:

### 2.1. The Hamiltonian ($H$): Economic Surplus

The Hamiltonian, $H(q, p)$, represents the total **Economic Surplus** of the ETF's idealized strategy. It is the fund's purely internal objective function based on its holdings ($q$) and internal valuations ($p$), representing what the fund manager perceives as the ideal, frictionless state. Since $p$ represents internal valuations rather than external market prices, $H(q,p)$ maintains separability from external market dynamics.

*   $H$ is a scalar function of the state $z$.
*   The gradient of the Hamiltonian, $\nabla H = [\frac{\partial H}{\partial q}, \frac{\partial H}{\partial p}]$, determines the conservative dynamics.
    *   $\frac{\partial H}{\partial p}$ represents the ideal **velocity** of holdings (trading activity).
    *   $\frac{\partial H}{\partial q}$ represents the ideal **force** to adjust the internal valuation based on holdings.
*   In Phase 2 of this project, $H$ will be learned from data by a **Hamiltonian Neural Network (HNN)**.

### 2.2. The Dissipation Matrix ($R$): Economic Frictions

The matrix $R(q, p)$ models the dissipative forces or **economic frictions** that cause the ETF to deviate from its ideal, energy-conserving strategy. $R$ must be positive semi-definite.

*   **Transaction Costs:** Proportional to trading volume.
*   **Market Impact:** The effect of the ETF's own trades on market prices.
*   **Slippage:** The difference between the expected and executed price.
*   **Management Fees & Operational Drag:** Internal costs that drain value.
*   In Phase 2, the structure of $R$ will be discovered from the residuals of the HNN model using **Symbolic Regression (PySR)**.

### 2.3. The Input Matrix ($g$) and External Inputs ($u$)

The term $g(q, p)u(t)$ represents how external forces and information are incorporated into the ETF's dynamics.

*   **External Inputs ($u$):** This vector contains exogenous variables that influence the fund manager's decisions. Crucially, this includes:
    *   The fund's proprietary **alpha signal**.
    *   **External market prices** $p_{market}$ (distinct from internal reservations $p$).
    *   **Price spread dynamics** $(p_{market} - p)$ capturing the tension between market consensus and internal valuation.
    *   Market volatility indices (e.g., VIX).
    *   Fund inflows/outflows.
*   **Input Matrix ($g$):** This matrix maps the external inputs $u$ to the state variables. It describes how the fund translates external information, alpha signals, and spread dynamics into trading decisions and internal valuation adjustments.
*   In Phase 2, PySR will discover $g$'s structure, particularly how spread dynamics $(p_{market} - p)$ create restoring forces that pull internal valuations toward market consensus.

## 3. Incorporating Stochastics

To capture the inherent randomness of financial markets, the deterministic PHS model is extended to a Stochastic Differential Equation (SDE) form:

$$dz = [(J-R)\nabla H + g \cdot u]dt + \Sigma(z)dW(t)$$

*   $dW(t)$ represents a Wiener process (or Brownian motion).
*   $\Sigma(z)$ is the diffusion matrix, which models the magnitude and structure of the random noise. Initially, this will be assumed to be a constant matrix $\Sigma$ to be estimated from the final model residuals.

## 4. The Complete Model

The full stochastic PHS model provides a complete, dynamic description of the ETF's behavior:

*   The **conservative core** ($J \nabla H$) describes the idealized, frictionless strategy of the fund, driven by its internal objective (Economic Surplus).
*   The **dissipative part** ($-R \nabla H$) describes how real-world frictions cause the fund to deviate from this ideal strategy. This includes costs from misalignment between internal valuations $p$ and external market prices.
*   The **external part** ($g \cdot u$) describes how the fund reacts to external market prices, alpha signals, and other external market conditions.
*   The **stochastic part** ($\Sigma dW$) captures the random fluctuations inherent in the market.

This formulation allows us to decompose the observed behavior of the ETF into its core objective, its operational frictions, its response to external drivers, and random noise, providing a deeply interpretable model.

## 5. Handling the Latent State in System Identification

### 5.1 The Challenge: Unobservable Internal Valuations

A critical methodological challenge arises from the fact that the internal reservation price $p$ is not directly observable—only external market prices $p_{market}$ are available in data. This creates a latent variable problem for the system identification pipeline.

### 5.2 The Dual-Role Solution

The methodology treats observable market prices $p_{market}$ in a dual capacity:

**Role 1: Noisy Measurement of Latent State**
$$p_{market}(t) = p(t) + \epsilon_p(t)$$

This assumption anchors the state estimation problem, providing a link between the observable and the latent internal valuation. It enables the identification engine to infer the most likely trajectory of $p(t)$ from observable data.

**Role 2: External Forcing Term**
The spread $(p_{market} - p)$ enters explicitly in the external input vector:
$$u(t) = [u_{alpha}(t), \, p_{market}(t) - p(t), \, \text{VIX}(t), \, \text{flows}(t)]^T$$

This captures the economic pressure that market consensus exerts on the fund's internal valuation, creating restoring forces discovered by PySR.

### 5.3 Practical Implementation Strategy

**Data Preprocessing**: Smooth both $q_{obs}(t)$ and $p_{market}(t)$ using splines to obtain continuous representations and derivatives.

**HNN Training**: Use $p_{market}(t)$ as the proxy input for the latent state $p(t)$, relying on the identification engine to perform joint:
- **Filtering**: Separate signal from noise in $p_{market}$ to recover $p$
- **Decomposition**: Identify PHS components $H$, $R$, and $g$
- **Discovery**: Find spread dynamics laws within $g$

**PySR Discovery**: Tasked specifically with discovering how $(p_{market} - p)$ influences system dynamics, potentially uncovering restoring force structures like $k_{spread} \cdot (p_{market} - p)$.

This approach transforms the latent variable challenge into an integrated part of the identification methodology, enabling the discovery of market interaction dynamics while maintaining theoretical consistency.
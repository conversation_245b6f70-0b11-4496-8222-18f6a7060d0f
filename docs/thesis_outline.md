# **The Structure of Strategy: A First-Principles Approach to Discovering the Dynamics of Active ETFs**

**Student:** <PERSON><PERSON><PERSON>  
**Supervisor:** Prof. <PERSON>  
**Date:** July 2025

---

## **Abstract**

This thesis develops and validates a novel, hybrid system identification pipeline designed to discover interpretable, first-principles laws governing an active ETF's interaction with its market environment. The research directly addresses the "Residual Diagnostics Puzzle" identified in prior work, where sophisticated Dynamic Factor Stochastic Volatility models fail due to misspecification of agent-specific dynamics.

The approach combines Economic Engineering principles with modern machine learning to create a transparent Port-Hamiltonian System (PHS) model that decomposes portfolio behavior into conservative dynamics (Economic Surplus $H$), dissipative frictions ($R$), and external market responses ($g$), targeting the ARK Innovation ETF (ARKK) as a case study.

The methodology unfolds in two phases: (1) Complete validation of the identification pipeline on synthetic data with formal structural identifiability proofs, and (2) Application to real ARKK data using an iterative Expectation-Maximization approach to simultaneously infer hidden internal states and discover symbolic physics through Hamiltonian Neural Networks and symbolic regression.

Key contributions include the first validated framework for empirically grounding Economic Engineering theory with financial market data, a novel solution to the latent state problem in portfolio dynamics, and interpretable insights into ETF strategy, costs, and market interactions through discovered symbolic laws.

---

## **Chapter 1: Introduction**

### **1.1 The Residual Diagnostics Puzzle: A Crisis of Misspecification**
- **The Problem:** Previous Master's thesis demonstrated that sophisticated DFSV models with Bellman Information Filters successfully capture aggregate market dynamics but fail diagnostic tests at the individual asset level
- **The Implication:** Significant agent-specific behavior remains unexplained in residuals after accounting for common market factors
- **The Challenge:** Need for interpretable models that can capture the dynamic interplay between internal strategy, real-world frictions, and external market forces

### **1.2 The Gap: From Theory to Data in Economic Engineering**
- **Theoretical Foundation:** Prof. Mendel's Economic Engineering framework provides sophisticated analogies (Hamiltonian mechanics, multiport networks) for economic systems
- **Missing Link:** Lack of validated methodology to empirically ground these theoretical constructs with real financial market data
- **Opportunity:** Bridge theory and practice through data-driven discovery of first-principles laws

### **1.3 The "Chicken-and-Egg" Problem in Financial System Identification**
- **The Latent State Problem:** PHS models require knowledge of agent's internal "economic momentum" (reservation price $p_{agent}$), which is unobservable
- **The Endogeneity Trap:** Using observable market prices as proxy creates methodological flaws, making separation of internal strategy from market reactions impossible
- **The Interdependency:** Cannot know physical laws without state trajectory, cannot robustly infer state trajectory without knowing physical laws

### **1.4 Research Questions**
- **RQ1:** How can an iterative, self-consistent discovery pipeline break the "chicken-and-egg" deadlock to simultaneously infer latent agent states and discover first-principles laws governing active ETF dynamics?
- **RQ2:** Can Port-Hamiltonian Systems with state variables $z = [q^T, p^T]^T$ (holdings and internal reservation prices) provide interpretable decomposition of ETF behavior into conservative surplus ($H$), dissipative frictions ($R$), and external market coupling ($g$)?
- **RQ3:** What insights into ARKK's true Economic Surplus function, operational cost structure, and market response mechanisms can be discovered through the validated identification pipeline?

### **1.5 Thesis Contributions**
- **Methodological Innovation:** First validated two-phase approach (synthetic validation → real application) for PHS system identification in finance
- **Theoretical Advancement:** Novel solution to latent state problem through hybrid PHS framework with EM-like iterative discovery
- **Empirical Breakthrough:** First application of learned PHS models with HNN/PySR to active ETF management
- **Practical Impact:** Transparent, interpretable model providing actionable insights for cost optimization, risk management, and strategy refinement

### **1.6 Thesis Structure**
Overview of the two-phase methodology and chapter organization.

---

## **Chapter 2: Literature Review & Theoretical Foundation**

### **2.1 The Residual Diagnostics Puzzle in Financial Modeling**
- Review of factor models and their limitations in capturing agent-specific dynamics
- Dynamic Factor Stochastic Volatility models and Bellman Information Filters
- Evidence of systematic misspecification in existing portfolio models

### **2.2 Port-Hamiltonian Systems: Mathematical Foundation**
- Hamiltonian mechanics and symplectic geometry fundamentals
- PHS formulation: $\dot{z} = (J-R(z))\nabla H(z) + g(z)u(t)$
- Energy conservation, passivity, and structure preservation properties
- Applications in engineering systems and control theory

### **2.3 Economic Engineering: Physical Analogies in Economics**
- **Prof. Mendel's Framework:**
  - Newtonian mechanics of demand: price as momentum, want as force
  - Lagrangian/Hamiltonian formulations: $H$ as Economic Surplus
  - Multiport networks: electrical circuit analogies ($L_{econ}, C_{econ}, R_{econ}$)
- **Canonical Variable Interpretation:** Holdings $q$ as positions, internal reservation prices $p$ as economic momentum
- **Market Completeness Assumption:** Factor model captures sufficient external market information

### **2.4 System Identification Challenges in Finance**
- Parameter estimation under noise, non-stationarity, and non-linearity
- The inverse problem: inferring structure from behavior
- Identifiability theory and practical considerations

### **2.5 Physics-Informed Machine Learning for Discovery**
- **Hamiltonian Neural Networks:** Learning conservative dynamics while preserving physical structure
- **Symbolic Regression (PySR):** Discovering interpretable mathematical laws from data
- **Extended Kalman Filters:** State estimation in non-linear systems

### **2.6 Advanced Frameworks (Context for Future Work)**
- Contact Hamiltonian Systems for non-conservative dynamics
- Port-Hamiltonian Neural Networks (pHNNs) for simultaneous component learning

---

## **Chapter 3: Theoretical Framework: PHS Model for Active ETFs**

### **3.1 State Variable Definition and Economic Interpretation**
- **Holdings Vector:** $q_i(t)$ representing asset quantities/weights in ETF portfolio
- **Internal Reservation Prices:** $p_i(t)$ as agent's private valuation (economic momentum)
- **State Vector:** $z = [q^T, p^T]^T$ enabling purely internal Hamiltonian formulation

### **3.2 The Hamiltonian: Economic Surplus Function**
- **Conceptual Interpretation:** $H(q,p)$ as ETF's revealed strategic utility/objective function
- **Physical Meaning:** Total "energy" of the system representing idealized, frictionless strategy
- **Expected Properties:** Convexity, relationship to portfolio theory (mean-variance, Kelly criterion)

### **3.3 Dissipation Structure: Friction and Transaction Costs**
- **Dissipation Matrix:** $R(q,p)$ capturing transaction costs, slippage, market impact
- **Physical Constraints:** Positive semi-definite ensuring energy dissipation (never creation)
- **Economic Interpretation:** Real-world frictions that prevent ideal strategy execution

### **3.4 External Coupling: Market Interaction**
- **Input Vector:** $u(t) = [f_t^T, h_t^T, u_{flows}^T]^T$ (market factors, volatilities, fund flows)
- **Coupling Matrix:** $g(q,p)$ defining how external forces influence agent state
- **Market Completeness:** Assumption that DFSV factors capture sufficient market information

### **3.5 Complete PHS Model Equation**
$$\frac{dz}{dt} = (J - R(q,p))\nabla H(q,p) + g(q,p)u(t)$$
where $J = \begin{pmatrix} 0 & I \\ -I & 0 \end{pmatrix}$ is the canonical symplectic matrix.

### **3.6 Stochastic Extension**
- **SDE Formulation:** $dz = [\text{PHS dynamics}]dt + \Sigma(z)dW(t)$
- **Initial Assumption:** Constant diffusion matrix $\Sigma$ for tractability

---

## **Chapter 4: Phase 1 - Pipeline Development & Validation on Synthetic Data**

### **4.1 Objective and Strategic Importance**
- **Go/No-Go Validation:** Prove methodology can recover true laws in controlled environment
- **Identifiability Establishment:** Formal proof that physical laws can be uniquely determined from partial observations
- **Algorithm Validation:** Test complete iterative discovery pipeline before applying to real data

### **4.2 Sub-Phase 1A: Linear Coupled Port-Hamiltonian System**

#### **4.2.1 Theoretical Model Design**
- **Agent-Market Coupling:** Two interconnected linear PHS subsystems
- **State Definition:** $z = [z_{agent}^T, z_{market}^T]^T$ with partial observability
- **Parameter Set:** $\theta = \{m, k, R_{params}, g_{params}\}$ for identification
- **Critical Challenge:** Identifiability of friction and coupling parameters from partial observations

#### **4.2.2 Implementation and Validation**
- **JAX/Optimistix Architecture:** Modular PHS simulator with automatic differentiation
- **Prediction Error Method:** Gradient-based parameter identification
- **Formal Analysis:** Transfer function approach to structural identifiability
- **Success Metrics:** Parameter recovery accuracy, convergence properties

### **4.3 Sub-Phase 1B: Non-Linear System with Iterative Discovery**

#### **4.3.1 Synthetic Non-Linear Agent Model**
- **Ground Truth Design:** Known non-linear $H$, symbolic $R(q,p)$, and $g(q,p)$
- **Latent State Problem:** Only $q_{obs}(t)$ observable, $p_{true}(t)$ hidden
- **Bootstrap Strategies:** Multiple initialization methods for momentum guess

#### **4.3.2 EM-Like Discovery Algorithm**
- **M-Step:** HNN training for Hamiltonian, PySR discovery of $R$ and $g$
- **E-Step:** EKF state update with learned dynamics
- **Convergence Analysis:** Stability across different initialization methods

#### **4.3.3 Critical Recovery Validation**
- **Hamiltonian Recovery:** Function approximation accuracy $H_{discovered}$ vs $H_{true}$
- **Symbolic Law Discovery:** PySR accuracy on functional forms
- **State Trajectory Recovery:** Latent momentum inference quality
- **Robustness Assessment:** Sensitivity to noise and initialization

### **4.4 Phase 1 Results and Insights**
- Performance benchmarks and limitations discovered
- Lessons learned for Phase 2 application
- Validated methodology ready for real data

---

## **Chapter 5: Phase 2 - Application to Real ARKK Data**

### **5.1 Market Environment Characterization**
- **Data Acquisition:** ARKK holdings (2014-2025), market prices, alpha signals, VIX, fund flows
- **DFSV-BIF Application:** Extract latent factor returns $f_t$ and log-volatilities $h_t$
- **Market State Construction:** $z_{market}(t) = [f_t^T, h_t^T]^T$ as external characterization

### **5.2 Data Preprocessing and Bootstrap Strategy**
- **Derivative Estimation:** Smoothing splines for analytical derivative calculation from noisy data
- **Economic Validation:** Trading volume consistency and microstructure plausibility checks
- **Initial State Bootstrap:** $p_0 = m \cdot \dot{q}_{obs}$ using inverse rolling volatility as inertia proxy

### **5.3 The Iterative Discovery Loop**

#### **5.3.1 EM Algorithm Implementation**
- **Initialization:** Bootstrap momentum, conservative assumption ($R_0=0, g_0=0$)
- **M-Step Sequence:**
  1. Train HNN on current state estimate for $H_{agent}$
  2. Calculate residual dynamics
  3. PySR discovery of symbolic $R$ and $g$ from residuals
- **E-Step:** EKF update of latent momentum trajectory using complete physics

#### **5.3.2 Convergence and Stability**
- **Convergence Criteria:** Symbolic law stability, L2-norm change < 1e-4
- **Robustness Checks:** Multiple initialization methods, noise sensitivity
- **Economic Plausibility:** Validation against financial theory and market microstructure

### **5.4 Technology Stack Integration**
- **JAX/Flax:** HNN implementation with conservation constraints
- **Diffrax:** Sophisticated ODE solvers for continuous-time dynamics
- **PySR/SymPy:** Symbolic discovery and expression manipulation
- **Custom EKF:** Automatic differentiation-enabled state estimation

---

## **Chapter 6: Discovery Results and Economic Interpretation**

### **6.1 The Learned Economic Surplus $H_{agent}(q,p)$**
- **Hamiltonian Landscape:** Visualization and interpretation of discovered strategy objective
- **Economic Meaning:** Revealed preferences, risk tolerance, optimal allocation patterns
- **Validation:** Consistency with portfolio theory and known ARKK strategy characteristics

### **6.2 Discovered Friction Laws $R(q,p)$**
- **Symbolic Forms:** Mathematical expressions discovered by PySR
- **Economic Interpretation:** Transaction costs, market impact, operational frictions
- **Cost Attribution:** Quantitative breakdown of different friction sources
- **Plausibility Assessment:** Consistency with market microstructure theory

### **6.3 Market Coupling Functions $g(q,p)$**
- **Symbolic Expressions:** How external market forces couple to agent state
- **Sensitivity Analysis:** ETF response to market factors, volatility, fund flows
- **Economic Insights:** Strategy adaptability, market timing, risk management

### **6.4 Latent Internal Price Trajectory $p_{agent}(t)$**
- **State Evolution:** Inferred internal reservation prices over time
- **Market Comparison:** Relationship between internal and observable market prices
- **Strategic Insights:** Timing of trades, value discovery, strategic patience

### **6.5 Model Performance and Validation**
- **Goodness-of-Fit:** In-sample trajectory matching and residual analysis
- **Out-of-Sample Testing:** Short-horizon forecasting ability
- **Stylized Facts:** Reproduction of known ETF behavioral patterns
- **Comparative Analysis:** Performance vs. linear PHS and traditional models

---

## **Chapter 7: Applications and Strategic Implications**

### **7.1 Enhanced Risk Management**
- **Transparent Risk Sources:** Decomposition into conservative, dissipative, and external components
- **Scenario Analysis:** Model response to different market conditions
- **Regime Identification:** Understanding strategy performance across market cycles

### **7.2 Strategy Diagnostics and Optimization**
- **Cost Structure Analysis:** Identification of optimization opportunities from $R(q,p)$
- **Performance Attribution:** Contributions from ideal strategy ($H$) vs. real-world constraints
- **Strategic Refinement:** Data-driven insights for strategy improvement

### **7.3 System Understanding and Control**
- **Interpretable Dynamics:** Clear mathematical laws governing portfolio behavior
- **Control Applications:** Potential for model-based optimization and control
- **Economic Engineering Validation:** Empirical grounding of theoretical constructs

---

## **Chapter 8: Discussion and Broader Implications**

### **8.1 Methodological Contributions**
- **Latent State Solution:** Novel approach to the "chicken-and-egg" problem in financial system identification
- **Validation Framework:** Two-phase methodology ensuring robustness before real-world application
- **Discovery Pipeline:** Integration of physics-informed ML with symbolic regression for interpretable laws

### **8.2 Economic Engineering Advancement**
- **Theory-Data Bridge:** First validated approach to empirically ground Economic Engineering models
- **Interpretability Preservation:** Maintaining economic meaning while leveraging advanced ML techniques
- **Scalability Potential:** Framework applicable to other financial systems and agents

### **8.3 Limitations and Challenges**
- **Market Completeness:** Dependence on DFSV factor model capturing sufficient market information
- **Identifiability Constraints:** Practical limits of parameter uniqueness in non-linear systems
- **Computational Complexity:** Scalability considerations for high-dimensional portfolios

### **8.4 Future Research Directions**
- **Extension to Other ETFs:** Validation across different investment strategies and market segments
- **Advanced Frameworks:** Contact Hamiltonian systems for non-conservative dynamics
- **Real-Time Applications:** Implementation for live portfolio management and optimization

---

## **Chapter 9: Conclusion**

### **9.1 Summary of Key Findings**
- Successful validation of iterative discovery pipeline on synthetic data
- Discovery of interpretable laws governing ARKK dynamics
- Solution to residual diagnostics puzzle through agent-specific modeling

### **9.2 Principal Contributions**
- **Methodological:** First validated framework for PHS identification in finance
- **Theoretical:** Novel solution to latent state problem in portfolio dynamics  
- **Empirical:** Interpretable insights into active ETF strategy and costs
- **Practical:** Transparent model enabling actionable risk management and optimization

### **9.3 Research Impact**
- Bridge between Economic Engineering theory and financial practice
- Foundation for data-driven discovery in quantitative finance
- Template for physics-informed modeling of complex financial systems

### **9.4 Final Remarks**
The work demonstrates that sophisticated theoretical frameworks can be empirically grounded while maintaining interpretability, opening new avenues for understanding and optimizing complex financial agent behavior.

---

## **Bibliography/References**

**Appendices**
- **A:** Mathematical Derivations and PHS Theory Details
- **B:** Identifiability Analysis and Formal Proofs  
- **C:** HNN Architecture and Training Specifications
- **D:** PySR Configuration and Discovered Expression Library
- **E:** Additional Validation Results and Diagnostic Tests
- **F:** Software Implementation Guide and Reproducibility Instructions
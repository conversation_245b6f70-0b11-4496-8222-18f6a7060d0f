<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1179.36pt" height="52.7pt" viewBox="0 0 1179.36 52.7" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-15T16:18:49.874490</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.3, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 52.7 
L 1179.36 52.7 
L 1179.36 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="line2d_1">
    <path d="M 13.68 24.98 
L 13.68 39.38 
L 4.68 39.38 
L 22.68 39.38 
M 7.38 43.7 
L 19.98 43.7 
M 11.88 48.02 
L 15.48 48.02 
" clip-path="url(#pd1587a8015)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_2">
    <path d="M 13.68 24.98 
L 301.68 24.98 
L 589.68 24.98 
" clip-path="url(#pd1587a8015)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_3">
    <path d="M 589.68 24.98 
L 877.68 24.98 
L 1165.68 24.98 
" clip-path="url(#pd1587a8015)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_4">
    <path d="M 1165.68 24.98 
L 1165.68 39.38 
L 1156.68 39.38 
L 1174.68 39.38 
M 1159.38 43.7 
L 1171.98 43.7 
M 1163.88 48.02 
L 1167.48 48.02 
" clip-path="url(#pd1587a8015)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="patch_2">
    <path d="M 13.68 27.68 
C 14.396048 27.68 15.082866 27.395511 15.589188 26.889188 
C 16.095511 26.382866 16.38 25.696048 16.38 24.98 
C 16.38 24.263952 16.095511 23.577134 15.589188 23.070812 
C 15.082866 22.564489 14.396048 22.28 13.68 22.28 
C 12.963952 22.28 12.277134 22.564489 11.770812 23.070812 
C 11.264489 23.577134 10.98 24.263952 10.98 24.98 
C 10.98 25.696048 11.264489 26.382866 11.770812 26.889188 
C 12.277134 27.395511 12.963952 27.68 13.68 27.68 
z
" clip-path="url(#pd1587a8015)" style="stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="text_1">
    <!-- $p(t)$ (Price/Incentive) -->
    <g transform="translate(228.16 15.74) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Oblique-70" d="M 3175 2156 
Q 3175 2616 2975 2859 
Q 2775 3103 2400 3103 
Q 2144 3103 1911 2972 
Q 1678 2841 1497 2591 
Q 1319 2344 1212 1994 
Q 1106 1644 1106 1300 
Q 1106 863 1306 627 
Q 1506 391 1875 391 
Q 2147 391 2380 519 
Q 2613 647 2778 891 
Q 2956 1147 3065 1494 
Q 3175 1841 3175 2156 
z
M 1394 2969 
Q 1625 3272 1939 3428 
Q 2253 3584 2638 3584 
Q 3175 3584 3472 3232 
Q 3769 2881 3769 2247 
Q 3769 1728 3584 1258 
Q 3400 788 3053 416 
Q 2822 169 2531 39 
Q 2241 -91 1919 -91 
Q 1547 -91 1294 64 
Q 1041 219 916 525 
L 556 -1331 
L -19 -1331 
L 922 3500 
L 1497 3500 
L 1394 2969 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-74" d="M 2706 3500 
L 2619 3053 
L 1472 3053 
L 1100 1153 
Q 1081 1047 1072 975 
Q 1063 903 1063 863 
Q 1063 663 1183 572 
Q 1303 481 1569 481 
L 2150 481 
L 2053 0 
L 1503 0 
Q 991 0 739 200 
Q 488 400 488 806 
Q 488 878 497 964 
Q 506 1050 525 1153 
L 897 3053 
L 409 3053 
L 500 3500 
L 978 3500 
L 1172 4494 
L 1747 4494 
L 1556 3500 
L 2706 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-2f" d="M 1625 4666 
L 2156 4666 
L 531 -594 
L 0 -594 
L 1625 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Oblique-70" transform="translate(0 0.015625)"/>
     <use xlink:href="#DejaVuSans-28" transform="translate(63.476562 0.015625)"/>
     <use xlink:href="#DejaVuSans-Oblique-74" transform="translate(102.490234 0.015625)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(141.699219 0.015625)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(180.712891 0.015625)"/>
     <use xlink:href="#DejaVuSans-28" transform="translate(212.5 0.015625)"/>
     <use xlink:href="#DejaVuSans-50" transform="translate(251.513672 0.015625)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(311.816406 0.015625)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(352.929688 0.015625)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(380.712891 0.015625)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(435.693359 0.015625)"/>
     <use xlink:href="#DejaVuSans-2f" transform="translate(497.216797 0.015625)"/>
     <use xlink:href="#DejaVuSans-49" transform="translate(530.908203 0.015625)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(560.400391 0.015625)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(623.779297 0.015625)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(678.759766 0.015625)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(740.283203 0.015625)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(803.662109 0.015625)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(842.871094 0.015625)"/>
     <use xlink:href="#DejaVuSans-76" transform="translate(870.654297 0.015625)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(929.833984 0.015625)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(991.357422 0.015625)"/>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pd1587a8015">
   <rect x="0" y="-0" width="1179.36" height="52.7"/>
  </clipPath>
 </defs>
</svg>

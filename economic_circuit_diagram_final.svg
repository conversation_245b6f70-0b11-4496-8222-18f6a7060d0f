<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="842.541562pt" height="386.96pt" viewBox="0 0 842.541562 386.96" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-15T16:20:42.837218</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.3, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 386.96 
L 842.541562 386.96 
L 842.541562 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 108.861563 130.28 
C 113.635218 130.28 118.214 128.383406 121.589485 125.007922 
C 124.964969 121.632438 126.861563 117.053656 126.861563 112.28 
C 126.861563 107.506344 124.964969 102.927562 121.589485 99.552078 
C 118.214 96.176594 113.635218 94.28 108.861563 94.28 
C 104.087907 94.28 99.509125 96.176594 96.13364 99.552078 
C 92.758156 102.927562 90.861563 107.506344 90.861563 112.28 
C 90.861563 117.053656 92.758156 121.632438 96.13364 125.007922 
C 99.509125 128.383406 104.087907 130.28 108.861563 130.28 
L 108.861563 130.28 
z
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_3">
    <path d="M 468.861562 310.28 
C 473.635218 310.28 478.214 308.383406 481.589485 305.007922 
C 484.964969 301.632438 486.861562 297.053656 486.861562 292.28 
C 486.861562 287.506344 484.964969 282.927562 481.589485 279.552078 
C 478.214 276.176594 473.635218 274.28 468.861562 274.28 
C 464.087907 274.28 459.509125 276.176594 456.13364 279.552078 
C 452.758156 282.927562 450.861562 287.506344 450.861562 292.28 
C 450.861562 297.053656 452.758156 301.632438 456.13364 305.007922 
C 459.509125 308.383406 464.087907 310.28 468.861562 310.28 
L 468.861562 310.28 
z
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 648.861562 103.28 
C 650.054579 103.28 651.199953 102.805571 652.043543 101.961981 
C 652.887133 101.11839 653.361562 99.973016 653.361562 98.78 
C 653.361562 97.586984 652.887133 96.44161 652.043543 95.598019 
C 651.199953 94.754429 650.054579 94.28 648.861562 94.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 648.861562 112.28 
C 650.054579 112.28 651.199953 111.805571 652.043543 110.961981 
C 652.887133 110.11839 653.361562 108.973016 653.361562 107.78 
C 653.361562 106.586984 652.887133 105.44161 652.043543 104.598019 
C 651.199953 103.754429 650.054579 103.28 648.861562 103.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_6">
    <path d="M 648.861562 121.28 
C 650.054579 121.28 651.199953 120.805571 652.043543 119.961981 
C 652.887133 119.11839 653.361562 117.973016 653.361562 116.78 
C 653.361562 115.586984 652.887133 114.44161 652.043543 113.598019 
C 651.199953 112.754429 650.054579 112.28 648.861562 112.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_7">
    <path d="M 648.861562 130.28 
C 650.054579 130.28 651.199953 129.805571 652.043543 128.961981 
C 652.887133 128.11839 653.361562 126.973016 653.361562 125.78 
C 653.361562 124.586984 652.887133 123.44161 652.043543 122.598019 
C 651.199953 121.754429 650.054579 121.28 648.861562 121.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="line2d_1">
    <path d="M 108.861563 202.28 
L 108.861563 130.28 
L 108.861563 130.28 
M 108.861563 94.28 
L 108.861563 94.28 
L 108.861563 22.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_2">
    <path d="M 112.461563 121.28 
L 105.261563 121.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_3">
    <path d="M 108.861563 106.88 
L 108.861563 99.68 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_4">
    <path d="M 112.461563 103.28 
L 105.261563 103.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_5">
    <path d="M 108.861563 22.28 
L 198.861563 22.28 
L 288.861562 22.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_6">
    <path d="M 288.861562 22.28 
L 288.861562 94.28 
L 297.861562 97.28 
L 279.861562 103.28 
L 297.861562 109.28 
L 279.861562 115.28 
L 297.861562 121.28 
L 279.861562 127.28 
L 288.861562 130.28 
L 288.861562 202.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_7">
    <path d="M 288.861562 202.28 
L 198.861562 202.28 
L 108.861563 202.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_8">
    <path d="M 288.861562 22.28 
L 378.861562 22.28 
L 468.861562 22.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_9">
    <path d="M 468.861562 22.28 
L 468.861562 109.04 
M 477.861562 109.04 
L 459.861562 109.04 
M 477.861562 115.52 
L 459.861562 115.52 
M 468.861562 115.52 
L 468.861562 202.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_10">
    <path d="M 468.861562 202.28 
L 468.861562 274.28 
L 468.861562 274.28 
M 468.861562 310.28 
L 468.861562 310.28 
L 468.861562 382.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_11">
    <path d="M 465.261562 283.28 
L 472.461562 283.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_12">
    <path d="M 468.861562 297.68 
L 468.861562 304.88 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_13">
    <path d="M 465.261562 301.28 
L 472.461562 301.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_14">
    <path d="M 468.861562 382.28 
L 288.861562 292.28 
L 108.861563 202.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_15">
    <path d="M 288.861562 22.28 
L 468.861562 22.28 
L 648.861562 22.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_16">
    <path d="M 648.861562 22.28 
L 648.861562 94.28 
M 648.861562 130.28 
L 648.861562 202.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_17">
    <path d="M 648.861562 202.28 
L 378.861562 202.28 
L 108.861563 202.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_18">
    <path d="M 288.861562 22.28 
L 558.861562 22.28 
L 828.861562 22.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_19">
    <path d="M 828.861562 22.28 
L 828.861562 94.28 
L 837.861562 97.28 
L 819.861562 103.28 
L 837.861562 109.28 
L 819.861562 115.28 
L 837.861562 121.28 
L 819.861562 127.28 
L 828.861562 130.28 
L 828.861562 202.28 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="line2d_20">
    <path d="M 828.861562 202.28 
L 828.861562 216.68 
L 819.861562 216.68 
L 837.861562 216.68 
M 822.561562 221 
L 835.161563 221 
M 827.061562 225.32 
L 830.661563 225.32 
" clip-path="url(#pd16b96c960)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="text_1">
    <!-- $u(t)$ -->
    <g transform="translate(61.921563 109.368437) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Oblique-75" d="M 428 1388 
L 838 3500 
L 1416 3500 
L 1006 1409 
Q 975 1256 961 1147 
Q 947 1038 947 966 
Q 947 700 1109 554 
Q 1272 409 1569 409 
Q 2031 409 2368 721 
Q 2706 1034 2809 1563 
L 3194 3500 
L 3769 3500 
L 3091 0 
L 2516 0 
L 2631 550 
Q 2388 244 2052 76 
Q 1716 -91 1338 -91 
Q 878 -91 622 161 
Q 366 413 366 863 
Q 366 956 381 1097 
Q 397 1238 428 1388 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-74" d="M 2706 3500 
L 2619 3053 
L 1472 3053 
L 1100 1153 
Q 1081 1047 1072 975 
Q 1063 903 1063 863 
Q 1063 663 1183 572 
Q 1303 481 1569 481 
L 2150 481 
L 2053 0 
L 1503 0 
Q 991 0 739 200 
Q 488 400 488 806 
Q 488 878 497 964 
Q 506 1050 525 1153 
L 897 3053 
L 409 3053 
L 500 3500 
L 978 3500 
L 1172 4494 
L 1747 4494 
L 1556 3500 
L 2706 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Oblique-75" transform="translate(0 0.125)"/>
     <use xlink:href="#DejaVuSans-28" transform="translate(63.378906 0.125)"/>
     <use xlink:href="#DejaVuSans-Oblique-74" transform="translate(102.392578 0.125)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(141.601562 0.125)"/>
    </g>
    <!-- Alpha Signal -->
    <g transform="translate(0 122.917812) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-41"/>
     <use xlink:href="#DejaVuSans-6c" transform="translate(68.408203 0)"/>
     <use xlink:href="#DejaVuSans-70" transform="translate(96.191406 0)"/>
     <use xlink:href="#DejaVuSans-68" transform="translate(159.667969 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(223.046875 0)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(284.326172 0)"/>
     <use xlink:href="#DejaVuSans-53" transform="translate(316.113281 0)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(379.589844 0)"/>
     <use xlink:href="#DejaVuSans-67" transform="translate(407.373047 0)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(470.849609 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(534.228516 0)"/>
     <use xlink:href="#DejaVuSans-6c" transform="translate(595.507812 0)"/>
    </g>
   </g>
   <g id="text_2">
    <!-- $p(t)$ (Price/Incentive) -->
    <g transform="translate(126.691563 15.74) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Oblique-70" d="M 3175 2156 
Q 3175 2616 2975 2859 
Q 2775 3103 2400 3103 
Q 2144 3103 1911 2972 
Q 1678 2841 1497 2591 
Q 1319 2344 1212 1994 
Q 1106 1644 1106 1300 
Q 1106 863 1306 627 
Q 1506 391 1875 391 
Q 2147 391 2380 519 
Q 2613 647 2778 891 
Q 2956 1147 3065 1494 
Q 3175 1841 3175 2156 
z
M 1394 2969 
Q 1625 3272 1939 3428 
Q 2253 3584 2638 3584 
Q 3175 3584 3472 3232 
Q 3769 2881 3769 2247 
Q 3769 1728 3584 1258 
Q 3400 788 3053 416 
Q 2822 169 2531 39 
Q 2241 -91 1919 -91 
Q 1547 -91 1294 64 
Q 1041 219 916 525 
L 556 -1331 
L -19 -1331 
L 922 3500 
L 1497 3500 
L 1394 2969 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-2f" d="M 1625 4666 
L 2156 4666 
L 531 -594 
L 0 -594 
L 1625 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Oblique-70" transform="translate(0 0.015625)"/>
     <use xlink:href="#DejaVuSans-28" transform="translate(63.476562 0.015625)"/>
     <use xlink:href="#DejaVuSans-Oblique-74" transform="translate(102.490234 0.015625)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(141.699219 0.015625)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(180.712891 0.015625)"/>
     <use xlink:href="#DejaVuSans-28" transform="translate(212.5 0.015625)"/>
     <use xlink:href="#DejaVuSans-50" transform="translate(251.513672 0.015625)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(311.816406 0.015625)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(352.929688 0.015625)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(380.712891 0.015625)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(435.693359 0.015625)"/>
     <use xlink:href="#DejaVuSans-2f" transform="translate(497.216797 0.015625)"/>
     <use xlink:href="#DejaVuSans-49" transform="translate(530.908203 0.015625)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(560.400391 0.015625)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(623.779297 0.015625)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(678.759766 0.015625)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(740.283203 0.015625)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(803.662109 0.015625)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(842.871094 0.015625)"/>
     <use xlink:href="#DejaVuSans-76" transform="translate(870.654297 0.015625)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(929.833984 0.015625)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(991.357422 0.015625)"/>
    </g>
   </g>
   <g id="text_3">
    <!-- $R_p$ -->
    <g transform="translate(259.881562 108.445312) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Oblique-52" d="M 1613 4147 
L 1294 2491 
L 2106 2491 
Q 2584 2491 2879 2755 
Q 3175 3019 3175 3444 
Q 3175 3784 2976 3965 
Q 2778 4147 2406 4147 
L 1613 4147 
z
M 2772 2241 
Q 2972 2194 3105 2009 
Q 3238 1825 3413 1275 
L 3809 0 
L 3144 0 
L 2778 1197 
Q 2638 1659 2453 1815 
Q 2269 1972 1888 1972 
L 1191 1972 
L 806 0 
L 172 0 
L 1081 4666 
L 2503 4666 
Q 3150 4666 3495 4373 
Q 3841 4081 3841 3531 
Q 3841 3044 3547 2687 
Q 3253 2331 2772 2241 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Oblique-52" transform="translate(0 0.09375)"/>
     <use xlink:href="#DejaVuSans-Oblique-70" transform="translate(69.482422 -16.3125) scale(0.7)"/>
    </g>
    <!-- (Market Impact) -->
    <g transform="translate(164.35125 123.423125) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-28"/>
     <use xlink:href="#DejaVuSans-4d" transform="translate(39.013672 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(125.292969 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(186.572266 0)"/>
     <use xlink:href="#DejaVuSans-6b" transform="translate(227.685547 0)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(281.970703 0)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(343.494141 0)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(382.703125 0)"/>
     <use xlink:href="#DejaVuSans-49" transform="translate(414.490234 0)"/>
     <use xlink:href="#DejaVuSans-6d" transform="translate(443.982422 0)"/>
     <use xlink:href="#DejaVuSans-70" transform="translate(541.394531 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(604.871094 0)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(666.150391 0)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(721.130859 0)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(760.339844 0)"/>
    </g>
   </g>
   <g id="text_4">
    <!-- $C_{econ} = 1/k$ -->
    <g transform="translate(383.461563 109.368437) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Oblique-43" d="M 4447 4306 
L 4319 3641 
Q 4019 3944 3683 4091 
Q 3347 4238 2956 4238 
Q 2422 4238 2017 3981 
Q 1613 3725 1319 3200 
Q 1131 2863 1032 2486 
Q 934 2109 934 1728 
Q 934 1091 1264 756 
Q 1594 422 2222 422 
Q 2656 422 3056 561 
Q 3456 700 3834 978 
L 3688 231 
Q 3316 72 2936 -9 
Q 2556 -91 2175 -91 
Q 1278 -91 773 396 
Q 269 884 269 1753 
Q 269 2309 461 2846 
Q 653 3384 1013 3828 
Q 1394 4300 1883 4525 
Q 2372 4750 3022 4750 
Q 3422 4750 3780 4639 
Q 4138 4528 4447 4306 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-65" d="M 3078 2063 
Q 3088 2113 3092 2166 
Q 3097 2219 3097 2272 
Q 3097 2653 2873 2875 
Q 2650 3097 2266 3097 
Q 1838 3097 1509 2826 
Q 1181 2556 1013 2059 
L 3078 2063 
z
M 3578 1613 
L 903 1613 
Q 884 1494 878 1425 
Q 872 1356 872 1306 
Q 872 872 1139 634 
Q 1406 397 1894 397 
Q 2269 397 2603 481 
Q 2938 566 3225 728 
L 3116 159 
Q 2806 34 2476 -28 
Q 2147 -91 1806 -91 
Q 1078 -91 686 257 
Q 294 606 294 1247 
Q 294 1794 489 2264 
Q 684 2734 1063 3103 
Q 1306 3334 1642 3459 
Q 1978 3584 2356 3584 
Q 2950 3584 3301 3228 
Q 3653 2872 3653 2272 
Q 3653 2128 3634 1964 
Q 3616 1800 3578 1613 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-63" d="M 3431 3366 
L 3316 2797 
Q 3109 2947 2876 3022 
Q 2644 3097 2394 3097 
Q 2119 3097 1870 3000 
Q 1622 2903 1453 2725 
Q 1184 2453 1037 2087 
Q 891 1722 891 1331 
Q 891 859 1127 628 
Q 1363 397 1844 397 
Q 2081 397 2348 469 
Q 2616 541 2906 684 
L 2797 116 
Q 2547 13 2283 -39 
Q 2019 -91 1741 -91 
Q 1044 -91 669 257 
Q 294 606 294 1253 
Q 294 1797 489 2255 
Q 684 2713 1069 3078 
Q 1331 3328 1684 3456 
Q 2038 3584 2456 3584 
Q 2700 3584 2940 3529 
Q 3181 3475 3431 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-6f" d="M 1625 -91 
Q 1009 -91 651 289 
Q 294 669 294 1325 
Q 294 1706 417 2101 
Q 541 2497 738 2766 
Q 1047 3184 1428 3384 
Q 1809 3584 2291 3584 
Q 2888 3584 3255 3212 
Q 3622 2841 3622 2241 
Q 3622 1825 3500 1412 
Q 3378 1000 3181 728 
Q 2875 309 2494 109 
Q 2113 -91 1625 -91 
z
M 891 1344 
Q 891 869 1089 633 
Q 1288 397 1691 397 
Q 2269 397 2648 901 
Q 3028 1406 3028 2181 
Q 3028 2634 2825 2865 
Q 2622 3097 2228 3097 
Q 1903 3097 1650 2945 
Q 1397 2794 1197 2484 
Q 1050 2253 970 1956 
Q 891 1659 891 1344 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-6e" d="M 3566 2113 
L 3156 0 
L 2578 0 
L 2988 2091 
Q 3016 2238 3031 2350 
Q 3047 2463 3047 2528 
Q 3047 2791 2881 2937 
Q 2716 3084 2419 3084 
Q 1956 3084 1622 2776 
Q 1288 2469 1184 1941 
L 800 0 
L 225 0 
L 903 3500 
L 1478 3500 
L 1363 2950 
Q 1603 3253 1940 3418 
Q 2278 3584 2650 3584 
Q 3113 3584 3367 3334 
Q 3622 3084 3622 2631 
Q 3622 2519 3608 2391 
Q 3594 2263 3566 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-6b" d="M 1172 4863 
L 1747 4863 
L 1197 2028 
L 3169 3500 
L 3916 3500 
L 1716 1825 
L 3322 0 
L 2625 0 
L 1131 1709 
L 800 0 
L 225 0 
L 1172 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Oblique-43" transform="translate(0 0.015625)"/>
     <use xlink:href="#DejaVuSans-Oblique-65" transform="translate(69.824219 -16.390625) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-63" transform="translate(112.890625 -16.390625) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-6f" transform="translate(151.376953 -16.390625) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-6e" transform="translate(194.204102 -16.390625) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-3d" transform="translate(260.786133 0.015625)"/>
     <use xlink:href="#DejaVuSans-31" transform="translate(364.057617 0.015625)"/>
     <use xlink:href="#DejaVuSans-2f" transform="translate(427.680664 0.015625)"/>
     <use xlink:href="#DejaVuSans-Oblique-6b" transform="translate(461.37207 0.015625)"/>
    </g>
    <!-- (Tracking Error) -->
    <g transform="translate(348.494375 122.917812) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-45" d="M 628 4666 
L 3578 4666 
L 3578 4134 
L 1259 4134 
L 1259 2753 
L 3481 2753 
L 3481 2222 
L 1259 2222 
L 1259 531 
L 3634 531 
L 3634 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-28"/>
     <use xlink:href="#DejaVuSans-54" transform="translate(39.013672 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(85.347656 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(126.460938 0)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(187.740234 0)"/>
     <use xlink:href="#DejaVuSans-6b" transform="translate(242.720703 0)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(300.630859 0)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(328.414062 0)"/>
     <use xlink:href="#DejaVuSans-67" transform="translate(391.792969 0)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(455.269531 0)"/>
     <use xlink:href="#DejaVuSans-45" transform="translate(487.056641 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(550.240234 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(589.603516 0)"/>
     <use xlink:href="#DejaVuSans-6f" transform="translate(628.466797 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(689.648438 0)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(730.761719 0)"/>
    </g>
   </g>
   <g id="text_5">
    <!-- $q_{ref}$ -->
    <g transform="translate(424.441562 289.34) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Oblique-71" d="M 2669 525 
Q 2438 222 2123 65 
Q 1809 -91 1428 -91 
Q 897 -91 595 267 
Q 294 625 294 1253 
Q 294 1759 480 2231 
Q 666 2703 1013 3078 
Q 1238 3322 1530 3453 
Q 1822 3584 2144 3584 
Q 2531 3584 2781 3431 
Q 3031 3278 3144 2969 
L 3244 3494 
L 3822 3494 
L 2888 -1319 
L 2309 -1319 
L 2669 525 
z
M 891 1338 
Q 891 875 1084 633 
Q 1278 391 1644 391 
Q 2188 391 2572 911 
Q 2956 1431 2956 2175 
Q 2956 2625 2757 2864 
Q 2559 3103 2188 3103 
Q 1916 3103 1684 2976 
Q 1453 2850 1281 2606 
Q 1100 2350 995 2006 
Q 891 1663 891 1338 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-72" d="M 2853 2969 
Q 2766 3016 2653 3041 
Q 2541 3066 2413 3066 
Q 1953 3066 1609 2717 
Q 1266 2369 1153 1784 
L 800 0 
L 225 0 
L 909 3500 
L 1484 3500 
L 1375 2956 
Q 1603 3259 1920 3421 
Q 2238 3584 2597 3584 
Q 2691 3584 2781 3573 
Q 2872 3563 2963 3538 
L 2853 2969 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-66" d="M 3059 4863 
L 2969 4384 
L 2419 4384 
Q 2106 4384 1964 4261 
Q 1822 4138 1753 3809 
L 1691 3500 
L 2638 3500 
L 2553 3053 
L 1606 3053 
L 1013 0 
L 434 0 
L 1031 3053 
L 481 3053 
L 563 3500 
L 1113 3500 
L 1159 3744 
Q 1278 4363 1576 4613 
Q 1875 4863 2516 4863 
L 3059 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Oblique-71"/>
     <use xlink:href="#DejaVuSans-Oblique-72" transform="translate(63.476562 -16.40625) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-65" transform="translate(92.255859 -16.40625) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-66" transform="translate(135.322266 -16.40625) scale(0.7)"/>
    </g>
    <!-- (Target) -->
    <g transform="translate(393.015937 302.917812) scale(0.14 -0.14)">
     <use xlink:href="#DejaVuSans-28"/>
     <use xlink:href="#DejaVuSans-54" transform="translate(39.013672 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(83.597656 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(144.876953 0)"/>
     <use xlink:href="#DejaVuSans-67" transform="translate(184.240234 0)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(247.716797 0)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(309.240234 0)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(348.449219 0)"/>
    </g>
   </g>
   <g id="text_6">
    <!-- $L_{econ} = m$ -->
    <g transform="translate(582.541562 109.368437) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-Oblique-4c" d="M 1075 4666 
L 1709 4666 
L 909 525 
L 3181 525 
L 3078 0 
L 172 0 
L 1075 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Oblique-6d" d="M 5747 2113 
L 5338 0 
L 4763 0 
L 5166 2094 
Q 5191 2228 5203 2325 
Q 5216 2422 5216 2491 
Q 5216 2772 5059 2928 
Q 4903 3084 4622 3084 
Q 4203 3084 3875 2770 
Q 3547 2456 3450 1953 
L 3066 0 
L 2491 0 
L 2900 2094 
Q 2925 2209 2937 2307 
Q 2950 2406 2950 2484 
Q 2950 2769 2794 2926 
Q 2638 3084 2363 3084 
Q 1938 3084 1609 2770 
Q 1281 2456 1184 1953 
L 800 0 
L 225 0 
L 909 3500 
L 1484 3500 
L 1375 2956 
Q 1609 3263 1923 3423 
Q 2238 3584 2597 3584 
Q 2978 3584 3223 3384 
Q 3469 3184 3519 2828 
Q 3781 3197 4126 3390 
Q 4472 3584 4856 3584 
Q 5306 3584 5551 3325 
Q 5797 3066 5797 2591 
Q 5797 2488 5784 2364 
Q 5772 2241 5747 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Oblique-4c" transform="translate(0 0.09375)"/>
     <use xlink:href="#DejaVuSans-Oblique-65" transform="translate(55.712891 -16.3125) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-63" transform="translate(98.779297 -16.3125) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-6f" transform="translate(137.265625 -16.3125) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-Oblique-6e" transform="translate(180.092773 -16.3125) scale(0.7)"/>
     <use xlink:href="#DejaVuSans-3d" transform="translate(246.674805 0.09375)"/>
     <use xlink:href="#DejaVuSans-Oblique-6d" transform="translate(349.946289 0.09375)"/>
    </g>
    <!-- (Market Mass) -->
    <g transform="translate(546.526562 122.917812) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-28"/>
     <use xlink:href="#DejaVuSans-4d" transform="translate(39.013672 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(125.292969 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(186.572266 0)"/>
     <use xlink:href="#DejaVuSans-6b" transform="translate(227.685547 0)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(281.970703 0)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(343.494141 0)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(382.703125 0)"/>
     <use xlink:href="#DejaVuSans-4d" transform="translate(414.490234 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(500.769531 0)"/>
     <use xlink:href="#DejaVuSans-73" transform="translate(562.048828 0)"/>
     <use xlink:href="#DejaVuSans-73" transform="translate(614.148438 0)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(666.248047 0)"/>
    </g>
   </g>
   <g id="text_7">
    <!-- $R_q$ -->
    <g transform="translate(799.881562 108.445312) scale(0.14 -0.14)">
     <use xlink:href="#DejaVuSans-Oblique-52" transform="translate(0 0.09375)"/>
     <use xlink:href="#DejaVuSans-Oblique-71" transform="translate(69.482422 -16.3125) scale(0.7)"/>
    </g>
    <!-- (Trade Friction) -->
    <g transform="translate(711.36875 123.423125) scale(0.14 -0.14)">
     <defs>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-28"/>
     <use xlink:href="#DejaVuSans-54" transform="translate(39.013672 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(85.347656 0)"/>
     <use xlink:href="#DejaVuSans-61" transform="translate(126.460938 0)"/>
     <use xlink:href="#DejaVuSans-64" transform="translate(187.740234 0)"/>
     <use xlink:href="#DejaVuSans-65" transform="translate(251.216797 0)"/>
     <use xlink:href="#DejaVuSans-20" transform="translate(312.740234 0)"/>
     <use xlink:href="#DejaVuSans-46" transform="translate(344.527344 0)"/>
     <use xlink:href="#DejaVuSans-72" transform="translate(394.796875 0)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(435.910156 0)"/>
     <use xlink:href="#DejaVuSans-63" transform="translate(463.693359 0)"/>
     <use xlink:href="#DejaVuSans-74" transform="translate(518.673828 0)"/>
     <use xlink:href="#DejaVuSans-69" transform="translate(557.882812 0)"/>
     <use xlink:href="#DejaVuSans-6f" transform="translate(585.666016 0)"/>
     <use xlink:href="#DejaVuSans-6e" transform="translate(646.847656 0)"/>
     <use xlink:href="#DejaVuSans-29" transform="translate(710.226562 0)"/>
    </g>
   </g>
   <g id="patch_8">
    <path d="M 108.861563 204.98 
C 109.577611 204.98 110.264428 204.695511 110.770751 204.189188 
C 111.277073 203.682866 111.561563 202.996048 111.561563 202.28 
C 111.561563 201.563952 111.277073 200.877134 110.770751 200.370812 
C 110.264428 199.864489 109.577611 199.58 108.861563 199.58 
C 108.145514 199.58 107.458697 199.864489 106.952374 200.370812 
C 106.446052 200.877134 106.161563 201.563952 106.161563 202.28 
C 106.161563 202.996048 106.446052 203.682866 106.952374 204.189188 
C 107.458697 204.695511 108.145514 204.98 108.861563 204.98 
z
" clip-path="url(#pd16b96c960)" style="stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pd16b96c960">
   <rect x="3.453563" y="-0" width="839.088" height="386.96"/>
  </clipPath>
 </defs>
</svg>

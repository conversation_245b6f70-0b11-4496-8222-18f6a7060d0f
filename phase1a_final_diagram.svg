<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="135.36pt" height="45.36pt" viewBox="0 0 135.36 45.36" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-11T18:43:08.069481</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.3, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 45.36 
L 135.36 45.36 
L 135.36 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 67.68 40.68 
C 72.453656 40.68 77.032438 38.783406 80.407922 35.407922 
C 83.783406 32.032438 85.68 27.453656 85.68 22.68 
C 85.68 17.906344 83.783406 13.327562 80.407922 9.952078 
C 77.032438 6.576594 72.453656 4.68 67.68 4.68 
C 62.906344 4.68 58.327562 6.576594 54.952078 9.952078 
C 51.576594 13.327562 49.68 17.906344 49.68 22.68 
C 49.68 27.453656 51.576594 32.032438 54.952078 35.407922 
C 58.327562 38.783406 62.906344 40.68 67.68 40.68 
L 67.68 40.68 
z
" clip-path="url(#padad35b600)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="line2d_1">
    <path d="M 4.68 22.68 
L 49.68 22.68 
L 49.68 22.68 
M 85.68 22.68 
L 85.68 22.68 
L 130.68 22.68 
" clip-path="url(#padad35b600)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: round"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="padad35b600">
   <rect x="0" y="0" width="135.36" height="45.36"/>
  </clipPath>
 </defs>
</svg>

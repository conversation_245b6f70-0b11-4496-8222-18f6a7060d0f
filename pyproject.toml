[project]
name = "phynance"
version = "0.1.0"
authors = [
    { name="<PERSON><PERSON><PERSON>", email="<EMAIL>" }, # Please update with your email
]
description = "Physics-informed discovery of first-principles laws governing active ETF dynamics using Port-Hamiltonian Systems."
requires-python = ">=3.12"
dependencies = [
    "numpy",
    "scipy",
    "pandas",
    "matplotlib",
    "jax",
    "jaxlib",
    "diffrax>=0.5.0",
    "equinox", # Diffrax ecosystem helper
    "pysr",
    "scikit-learn",
    "jupyterlab",
    "pyyaml", # For config file loading
    # Consider adding specific versions for reproducibility, e.g., "numpy>=1.20"
    "schemdraw>=0.20",
    "sympy>=1.14.0",
    "pytest>=8.4.1",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "ruff",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = ["-v"]

[tool.ruff]
line-length = 88
select = ["E", "F", "W", "I001"]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

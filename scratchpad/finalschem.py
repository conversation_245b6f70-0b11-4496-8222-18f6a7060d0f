import schemdraw
import schemdraw.elements as elm
import schemdraw.dsp as dsp

def create_phase1a_definitive_diagram():
    """
    Create a clean, publication-quality Phase 1A diagram using a robust anchor-based approach.

    This demonstrates the key principles:
    1. Place one anchor element first (agent_block)
    2. Chain everything else off named anchors (.N, .S, .E, .W)
    3. Use .at() and .to() for connections
    4. Let schemdraw calculate paths automatically
    5. Use programmatic bounding box for perfect framing
    """
    d = schemdraw.Drawing()
    d.config(unit=3.5, fontsize=12)

    # Title
    d += elm.Label().label('Phase 1A: Coupled Port-Hamiltonian Systems').scale(1.3)
    d += elm.Gap().down(1.2)

    # === 1) ANCHOR ELEMENT: Place agent block first ===
    agent_label = 'AGENT PHS\\n$\\dot{z}_a=(J_a-R_a)M_a z_a + u_a$'
    agent_block = d.add(dsp.Box(w=4.5, h=2.6).label(agent_label))

    # === 2) CHAIN: Market block relative to agent ===
    d += elm.Gap().right(3)
    market_label = 'MARKET PHS\\n$\\dot{z}_m=(J_m-R_m)M_m z_m + u_m + w(t)$'
    market_block = d.add(dsp.Box(w=4.5, h=2.6).label(market_label))

    # === 3) ANCHOR-BASED: External noise using market.N anchor ===
    d.push()
    d += elm.Line().at(market_block.N).up(1.8)
    noise_point = d.add(elm.Dot())
    d += elm.Label().label('$w(t)$ (noise)', loc='top')
    d += elm.Arrow().down(1.8).to(market_block.N)
    d.pop()

    # === 4) ANCHOR-BASED: Coupling paths using named anchors ===
    # Agent momentum -> Market (top path)
    d.push()
    d += elm.Line().at(agent_block.N).up(1.2)
    agent_tap = d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='left')
    d += elm.Line().right().tox(market_block.N)
    d += elm.Arrow().down(1.2).to(market_block.N)
    d += elm.Label().label('$r_{coupling}$', loc='top')
    d.pop()

    # Market momentum -> Agent (bottom path)
    d.push()
    d += elm.Line().at(market_block.S).down(1.2)
    market_tap = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$', loc='right')
    d += elm.Line().left().tox(agent_block.S)
    d += elm.Arrow().up(1.2).to(agent_block.S)
    d += elm.Label().label('$r_{coupling}$', loc='bottom')
    d.pop()

    # === 5) PROGRAMMATIC: Perfect bounding box ===
    bbox = d.get_bbox()
    margin = 0.7
    x0, y0 = bbox.xmin - margin, bbox.ymax + margin
    x1, y1 = bbox.xmax + margin, bbox.ymin - margin

    # Draw perfect dashed boundary
    d.push()
    d += elm.Line().at((x0, y0)).to((x1, y0)).linestyle('--').color('gray')
    d += elm.Line().to((x1, y1)).linestyle('--').color('gray')
    d += elm.Line().to((x0, y1)).linestyle('--').color('gray')
    d += elm.Line().to((x0, y0)).linestyle('--').color('gray')
    d.pop()

    d += elm.Label().label('True Unobserved System').at(((x0+x1)/2, y0 + 0.3)).color('gray')

    # === 6) ANCHOR-BASED: Observations using block anchors ===
    y_out = y1 - 1.0

    # q_agent observation - starts from agent_block.S anchor
    d.push()
    d += elm.Line().at(agent_block.S).toy(y_out)
    d.add(elm.Dot())
    d += elm.Arrow().right(0.5).label('$y_0=q_{agent}$', loc='bottom')
    d.pop()

    # p_market observation - starts from market_block.S anchor
    d.push()
    d += elm.Line().at(market_block.S).toy(y_out)
    d.add(elm.Dot())
    d += elm.Arrow().right(0.5).label('$y_1=p_{market}$', loc='bottom')
    d.pop()

    # Final note
    d += elm.Label().label('Partial Observations: y = [q_agent, p_market]').at(((x0+x1)/2, y_out - 0.6))

    # Render
    d.save('phase1a_final_diagram.svg')
    d.draw()

    return d


if __name__ == "__main__":
    create_phase1a_definitive_diagram()
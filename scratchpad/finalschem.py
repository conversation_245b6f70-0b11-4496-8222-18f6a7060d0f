import schemdraw
import schemdraw.elements as elm
import schemdraw.dsp as dsp

def create_phase1a_definitive_diagram():
    """
    Creates a single, clean, and professional control-style block diagram for Phase 1A.
    This version is ideal for a thesis or presentation, balancing detail with clarity.
    It uses a robust, anchor-based approach for perfect connections and layout.
    """
    with schemdraw.Drawing(file='phase1a_final_diagram.svg', show=True, unit=3.5, fontsize=12) as d:
        
        # Add a clear title for context
        d.add(elm.Source().at((-3.5, 6)).right().label('Phase 1A: Linear De-coupling Gateway', loc='right', bold=True, fontsize=16))
        
        # === 1. DEFINE & PLACE THE CORE BLOCKS ===
        # We place the agent block first, then the market block relative to it.
        
        agent_label = 'AGENT PHS\n$z_a = [q_a, p_a]^T$'
        agent_block = d.add(dsp.Box(w=4.5, h=3).label(agent_label).at((0,0)))

        market_label = 'MARKET PHS\n$z_m = [q_m, p_m]^T$'
        market_block = d.add(dsp.Box(w=4.5, h=3).at((8,0)))

        # === 2. ADD EXTERNAL NOISE INPUT ===
        # The noise input is anchored to the top of the market block.
        
        d += (noise_arrow_end := elm.Arrow().down(1.5).at(market_block.N).label('$w(t)$\n(Stochastic Order Flow)', loc='top'))

        # === 3. DRAW THE COUPLING FEEDBACK PATHS ===
        # This is the key: we draw lines FROM and TO named anchors.
        
        # Path: Market Momentum -> Agent Input (Bottom Loop)
        d += (line_start := elm.Line().at(market_block.S).down(1.5).dot())
        d += elm.Line().left().tox(agent_block.S)
        d += elm.Arrow().to(agent_block.S).label('$p_{market}$ feeds into agent dynamics', loc='bottom', color='gray', fontsize=10)

        # Path: Agent Momentum -> Market Input (Top Loop)
        d += (line_start_2 := elm.Line().at(agent_block.N).up(1.5).dot())
        d += elm.Line().right().tox(market_block.N)
        # Connect to the exact same point as the noise input for clarity
        d += elm.Arrow().to(noise_arrow_end.end).label('$p_{agent}$ feeds into market dynamics', loc='top', color='gray', fontsize=10)
        
        # === 4. ADD THE "VEIL OF OBSERVATION" BOUNDARY ===
        # We calculate the box size programmatically for a perfect fit.
        
        bbox = d.get_bbox()
        margin = 0.75
        
        # Add a dashed box around all drawn elements
        veil_box = elm.Box(
            w=bbox.xmax - bbox.xmin + 2*margin,
            h=bbox.ymax - bbox.ymin + 2*margin
        ).at((bbox.xmin - margin, bbox.ymin - margin)).linestyle('--').color('gray').zorder(0)
        d.add(veil_box)
        
        d.add(elm.Label().label('True Unobserved System').at(veil_box.N).anchor('S').color('gray').move_up(0.2))

        # === 5. DRAW THE OBSERVATION OUTPUTS ===
        # These signals "pierce" the veil to the observer.
        
        # Observation of q_agent
        obs_y0_start = agent_block.S.midpoint(agent_block.S) # Start from middle of the south side
        d += elm.Line().at(obs_y0_start).down(0.5) # Small stub inside the veil
        d += (line_q := elm.Line().toy(veil_box.S).down(1).dot())
        d += elm.Arrow().label('$y_0 = q_{agent}$', loc='bottom', bold=True)

        # Observation of p_market
        obs_y1_start = market_block.S.midpoint(market_block.S)
        d += elm.Line().at(obs_y1_start).down(0.5)
        d += (line_p := elm.Line().toy(veil_box.S).down(1).dot())
        d += elm.Arrow().label('$y_1 = p_{market}$', loc='bottom', bold=True)
        
        # Add a final label for the output vector
        d.add(elm.Label().label(r'Observations: $\mathbf{y} = [q_{agent}, p_{market}]^T$').at((line_q.end[0]+3, line_q.end[1]-1)))


if __name__ == "__main__":
    create_final_presentation_diagram()
import schemdraw
import schemdraw.elements as elm
import schemdraw.dsp as dsp

def create_phase1a_publication_diagram():
    """
    Creates a final, publication-quality diagram for Phase 1A.
    This version features improved symmetry, clarity, and follows standard
    control system diagram conventions.
    """
    with schemdraw.Drawing(file='phase1a_publication_diagram.svg', show=True, unit=4, fontsize=14) as d:
        
        # === 1. Define and Place the Core Blocks ===
        # Establish the two main systems with a generous gap between them.
        
        agent_label = 'AGENT PHS\n$\\dot{z}_a=(J_a-R_a)M_a z_a + u_a$'
        agent_block = d.add(dsp.Box(w=4.5, h=2.5).label(agent_label).at((0,0)))

        market_label = 'MARKET PHS\n$\\dot{z}_m=(J_m-R_m)M_m z_m + u_m$'
        market_block = d.add(dsp.Box(w=4.5, h=2.5).at((9,0)))

        # === 2. Define Inputs (Coupling and Noise) ===
        # Inputs are grouped at a summation junction for clarity.
        
        # Summation junction for Market inputs
        sum_market = d.add(dsp.Sum().at(market_block.N).up(1.5).right(0.5))
        d.add(elm.Arrow().at(sum_market.S).to(market_block.N))

        # External Noise w(t) enters the summation junction from the top
        d.add(elm.Arrow().at(sum_market.N).up(1).label('$w(t)$ (Noise)', loc='top').to(sum_market.N))

        # === 3. Draw the Coupling Feedback Paths ===
        # Using .at() and .to() with anchors for perfect, robust connections.
        
        # Path 1: Agent Momentum -> Market Input (Top Loop)
        # The signal p_agent is tapped from the Agent block and fed into the Market's summation junction.
        d += (p_agent_tap := elm.Line().at(agent_block.N).up(0.8).dot().label('$p_{agent}$', loc='lft'))
        d += elm.Line().right().tox(sum_market.W)
        d += elm.Arrow().to(sum_market.W).label('Coupling', loc='top', color='gray', fontsize=11)

        # Path 2: Market Momentum -> Agent Input (Bottom Loop)
        # The signal p_market is tapped from the Market block and fed back to the Agent.
        d += (p_market_tap := elm.Line().at(market_block.S).down(0.8).dot().label('$p_{market}$', loc='rgt'))
        d += elm.Line().left().tox(agent_block.S)
        d += elm.Arrow().to(agent_block.S).label('Coupling', loc='bottom', color='gray', fontsize=11)
        
        # === 4. Add the "Veil of Observation" Boundary ===
        # Programmatically calculate the bounding box for a perfect fit.
        
        bbox = d.get_bbox()
        margin = 0.6
        veil_box = elm.Box(
            w=bbox.xmax - bbox.xmin + 2*margin,
            h=bbox.ymax - bbox.ymin + 2*margin
        ).at((bbox.xmin - margin, bbox.ymin - margin)).linestyle('--').color('gray').zorder(0)
        d.add(veil_box)
        d.add(elm.Label().label('True Unobserved System').at(veil_box.N).anchor('S').color('gray').move_up(0.2))

        # === 5. Draw the Observation Outputs ===
        # The only signals that "pierce" the veil.
        
        y_out_level = veil_box.S[1] - 1.2 # Define a consistent vertical level for outputs

        # Observation of q_agent
        # We tap the q_agent signal from its known source (inside the block, conceptually)
        q_agent_tap = agent_block.S
        d += elm.Line().at(q_agent_tap).toy(y_out_level).dot()
        d += elm.Arrow().label('$y_0 = q_{agent}$', loc='bottom', bold=True)

        # Observation of p_market
        # We tap the p_market signal from the already-defined tap point.
        d += elm.Line().at(p_market_tap.end).toy(y_out_level).dot()
        d += elm.Arrow().label('$y_1 = p_{market}$', loc='bottom', bold=True)
        
        # Add a final label for the output vector
        d.add(elm.Label().label('Partial Observations: $\mathbf{y} = [q_{agent}, p_{market}]^T$').at((d.unit/2, y_out_level-0.8)))


if __name__ == "__main__":
    create_phase1a_publication_diagram()
import schemdraw
import schemdraw.elements as elm
import schemdraw.dsp as dsp

def create_phase1a_publication_diagram():
    """
    Creates a final, publication-quality diagram for Phase 1A.
    This version features improved symmetry, clarity, and follows standard
    control system diagram conventions with perfect anchor-based connections.
    """
    d = schemdraw.Drawing()
    d.config(unit=4, fontsize=14)

    # Title with generous spacing
    d += elm.Label().label('Phase 1A: Coupled Port-Hamiltonian Systems').scale(1.4)
    d += elm.Gap().down(1.8)

    # === 1. Core Blocks with Generous White Space ===
    agent_label = 'AGENT PHS\\n$\\dot{z}_a=(J_a-R_a)M_a z_a + u_a$'
    agent_block = d.add(dsp.Box(w=4.5, h=2.8).label(agent_label))

    # Generous horizontal spacing for symmetry
    d += elm.Gap().right(4.5)
    market_label = 'MARKET PHS\\n$\\dot{z}_m=(J_m-R_m)M_m z_m + u_m$'
    market_block = d.add(dsp.Box(w=4.5, h=2.8).label(market_label))

    # === 2. Market Input Summation Junction ===
    # Consolidate all market inputs at a single summation point
    d.push()
    d += elm.Line().at(market_block.N).up(2.2)
    sum_market = d.add(dsp.Sum())

    # Connect summation to market input
    d += elm.Arrow().down(2.2).to(market_block.N)
    d.pop()

    # === 3. External Noise Input ===
    d.push()
    d += elm.Line().at(sum_market.N).up(1.5)
    d.add(elm.Dot())
    d += elm.Label().label('$w(t)$ (External Noise)', loc='top')
    d += elm.Arrow().down(1.5)
    d.pop()

    # === 4. Agent Momentum Signal Path (Top Loop) ===
    # Explicitly label the signal line before coupling
    d.push()
    d += elm.Line().at(agent_block.N).up(1.5)
    p_agent_tap = d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='left')

    # Horizontal coupling path with clear labeling
    d += elm.Line().right().tox(sum_market.W)
    d += elm.Arrow().to(sum_market.W)
    d += elm.Label().label('$r_{coupling} \\cdot p_{agent}$', loc='top')
    d.pop()

    # === 5. Market Momentum Signal Path (Bottom Loop) ===
    # Explicitly label the signal line before coupling
    d.push()
    d += elm.Line().at(market_block.S).down(1.5)
    p_market_tap = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$', loc='right')

    # Horizontal coupling path with clear labeling
    d += elm.Line().left().tox(agent_block.S)
    d += elm.Arrow().to(agent_block.S)
    d += elm.Label().label('$r_{coupling} \\cdot p_{market}$', loc='bottom')
    d.pop()

    # === 6. System Boundary (Veil of Observation) ===
    bbox = d.get_bbox()
    margin = 0.8
    x0, y0 = bbox.xmin - margin, bbox.ymax + margin
    x1, y1 = bbox.xmax + margin, bbox.ymin - margin

    # Perfect dashed boundary
    d.push()
    d += elm.Line().at((x0, y0)).to((x1, y0)).linestyle('--').color('gray')
    d += elm.Line().to((x1, y1)).linestyle('--').color('gray')
    d += elm.Line().to((x0, y1)).linestyle('--').color('gray')
    d += elm.Line().to((x0, y0)).linestyle('--').color('gray')
    d.pop()

    d += elm.Label().label('True Unobserved System').at(((x0+x1)/2, y0 + 0.4)).color('gray')

    # === 7. Observation Outputs ===
    y_out = y1 - 1.5

    # q_agent observation (position from agent)
    d.push()
    d += elm.Line().at(agent_block.S).toy(y_out)
    d.add(elm.Dot())
    d += elm.Arrow().right(0.8)
    d += elm.Label().label('$y_0 = q_{agent}$', loc='bottom')
    d.pop()

    # p_market observation (momentum from market)
    d.push()
    d += elm.Line().at(market_block.S).toy(y_out)
    d.add(elm.Dot())
    d += elm.Arrow().right(0.8)
    d += elm.Label().label('$y_1 = p_{market}$', loc='bottom')
    d.pop()

    # Final observation vector label
    d += elm.Label().label('Partial Observations: y = [q_agent, p_market]').at(((x0+x1)/2, y_out - 0.8))

    # Render
    d.save('phase1a_publication_diagram.svg')
    d.draw()

    return d


if __name__ == "__main__":
    create_phase1a_publication_diagram()
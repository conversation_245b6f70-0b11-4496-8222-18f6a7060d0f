<mxfile host="Electron" agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.9 Chrome/134.0.6998.205 Electron/35.7.4 Safari/537.36" version="27.0.9">
  <diagram name="Page-1" id="Yc_UzXKeyrIyOl_vHFCV">
    <mxGraphModel dx="1181" dy="727" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="1" shadow="0" adaptiveColors="auto">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-41" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;dashed=1;dashPattern=12 12;strokeColor=#6c8ebf;verticalAlign=bottom;fillStyle=auto;opacity=50;glass=0;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="240" width="590" height="390" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="sbOJOyDeGrx3KrOl7CLP-8" target="sbOJOyDeGrx3KrOl7CLP-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-36" value="\(p_a\)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sbOJOyDeGrx3KrOl7CLP-33">
          <mxGeometry x="-0.4044" y="1" relative="1" as="geometry">
            <mxPoint x="-11" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="sbOJOyDeGrx3KrOl7CLP-8">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="700" as="targetPoint" />
            <Array as="points">
              <mxPoint x="115" y="700" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-48" value="\(q_a\)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sbOJOyDeGrx3KrOl7CLP-47">
          <mxGeometry x="-0.725" relative="1" as="geometry">
            <mxPoint x="-12" y="-34" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-8" value="&lt;div&gt;Agent PHS&amp;nbsp;&lt;/div&gt;&lt;div&gt;\(z_a=[q_a,p_a]^T\)&lt;/div&gt;\(\dot{z_a}=(J_a-R_a)\nabla H_a\)" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="330" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="sbOJOyDeGrx3KrOl7CLP-9" target="sbOJOyDeGrx3KrOl7CLP-34">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-37" value="\(p_m\)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sbOJOyDeGrx3KrOl7CLP-35">
          <mxGeometry x="-0.526" y="-1" relative="1" as="geometry">
            <mxPoint x="14" y="-7" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="sbOJOyDeGrx3KrOl7CLP-9">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="330" y="700" as="targetPoint" />
            <mxPoint x="425" y="510" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="425" y="700" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-9" value="&lt;div&gt;Market Maker PHS&lt;/div&gt;&lt;div&gt;\(z_m=[q_m,p_m]^T\)&lt;/div&gt;\(\dot{z_m}=(J_m-R_m)\nabla H_m + E dW \)" style="rounded=0;whiteSpace=wrap;html=1;align=center;" vertex="1" parent="1">
          <mxGeometry x="330" y="330" width="190" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-10" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="sbOJOyDeGrx3KrOl7CLP-30">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="426" y="200" as="sourcePoint" />
            <mxPoint x="425.5" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-11" value="Noise \(w(t)\)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="sbOJOyDeGrx3KrOl7CLP-10">
          <mxGeometry x="-0.781" y="-1" relative="1" as="geometry">
            <mxPoint y="-20" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="sbOJOyDeGrx3KrOl7CLP-30" target="sbOJOyDeGrx3KrOl7CLP-9">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="425.51428571428573" y="249.98515769944333" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-30" value="E" style="triangle;whiteSpace=wrap;html=1;rotation=90;horizontal=0;" vertex="1" parent="1">
          <mxGeometry x="413" y="270" width="25" height="40" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="sbOJOyDeGrx3KrOl7CLP-32" target="sbOJOyDeGrx3KrOl7CLP-9">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="280" y="505" />
              <mxPoint x="280" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-32" value="\(r_{coupling}\)" style="triangle;whiteSpace=wrap;html=1;rotation=0;" vertex="1" parent="1">
          <mxGeometry x="180" y="460" width="70" height="90" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="sbOJOyDeGrx3KrOl7CLP-34" target="sbOJOyDeGrx3KrOl7CLP-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-34" value="\(r_{coupling}\)" style="triangle;whiteSpace=wrap;html=1;rotation=0;flipH=1;" vertex="1" parent="1">
          <mxGeometry x="330" y="460" width="70" height="90" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-50" value="&amp;quot;True, unobserved&amp;quot; system, the full Hamiltonian is \(H=H_a+H_m\)" style="text;align=center;verticalAlign=middle;rounded=0;whiteSpace=wrap;html=1;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="225" y="570" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sbOJOyDeGrx3KrOl7CLP-51" value="Observations&lt;br&gt;\(y(t)=[q_a,p_m]^T\)" style="text;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=default;html=1;" vertex="1" parent="1">
          <mxGeometry x="240" y="680" width="60" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

import schemdraw
import schemdraw.elements as elm
import schemdraw.dsp as dsp

def create_final_presentation_diagram():
    """
    Creates a single, clean, and professional control-style block diagram for Phase 1A.
    This version is ideal for a thesis or presentation, balancing detail with clarity.
    It uses an anchor-based approach for robust and clean connections.
    """
    d = schemdraw.Drawing()
    d.config(unit=4, fontsize=14)

    # Add title
    d += elm.Label().label('Phase 1A: Coupled Port-Hamiltonian Systems').scale(1.4)
    d += elm.Gap().down(1.5)

    # === 1. DEFINE THE CORE BLOCKS (THE ACTORS) ===
    # We define the main components first using simple positioning

    agent_label = 'AGENT PHS\\n$\\dot{z}_a = (J_a-R_a)M_a z_a + u_a$'
    agent_block = d.add(dsp.Box(w=5, h=2.5).label(agent_label))

    market_label = 'MARKET PHS\\n$\\dot{z}_m = (J_m-R_m)M_m z_m + u_m + w(t)$'
    d += elm.Gap().right(3)
    market_block = d.add(dsp.Box(w=5, h=2.5).label(market_label))

    # === 2. ADD EXTERNAL INPUTS (NOISE) ===
    # Clean noise input

    d.push()
    d += elm.Gap().right(-8).up(3.5)
    d.add(elm.Dot())
    d += elm.Label().label('$w(t)$ (External Noise)', loc='top')
    d += elm.Arrow().down(1.5)
    d.pop()

    # === 3. DRAW THE COUPLING PATHS ===
    # Clean, symmetric coupling paths showing momentum exchange

    # Path 1: Agent momentum -> Market (top path)
    d.push()
    d += elm.Gap().right(-8).up(2)
    d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='left')
    d += elm.Line().right(8)
    d += elm.Arrow().down(2)
    d += elm.Label().label('$r_{coupling}$', loc='top')
    d.pop()

    # Path 2: Market momentum -> Agent (bottom path)
    d.push()
    d += elm.Gap().down(2)
    d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$', loc='right')
    d += elm.Line().left(8)
    d += elm.Arrow().up(2)
    d += elm.Label().label('$r_{coupling}$', loc='bottom')
    d.pop()

    # === 4. ADD SYSTEM BOUNDARY ===
    # Professional dashed boundary showing the "veil of observation"

    d.push()
    d += elm.Gap().right(-9).up(4)
    d += elm.Line().right(14).linestyle('--').color('gray')
    d += elm.Line().down(8).linestyle('--').color('gray')
    d += elm.Line().left(14).linestyle('--').color('gray')
    d += elm.Line().up(8).linestyle('--').color('gray')
    d.pop()

    # System label
    d.push()
    d += elm.Gap().right(-2).up(4.5)
    d += elm.Label().label('True Unobserved System').color('gray').scale(1.1)
    d.pop()

    # === 5. DRAW THE OBSERVATION OUTPUTS ===
    # Clean observation outputs that "pierce the veil"

    # Observation of q_agent
    d.push()
    d += elm.Gap().right(-8).down(3.5)
    d.add(elm.Dot())
    d += elm.Label().label('$y_0 = q_{agent}$ (observed)', loc='bottom')
    d += elm.Line().up(3.5)
    d.pop()

    # Observation of p_market
    d.push()
    d += elm.Gap().down(3.5)
    d.add(elm.Dot())
    d += elm.Label().label('$y_1 = p_{market}$ (observed)', loc='bottom')
    d += elm.Line().up(3.5)
    d.pop()

    # Add observation note
    d.push()
    d += elm.Gap().right(-3).down(5)
    d += elm.Label().label('Partial Observations: $y = [q_{agent}, p_{market}]^T$').scale(1.1)
    d.pop()

    # Save and show
    d.save('phase1a_final_diagram.svg')
    d.draw()

    return d


if __name__ == "__main__":
    create_final_presentation_diagram()
import schemdraw
import schemdraw.elements as elm
import schemdraw.dsp as dsp

def create_final_presentation_diagram():
    """
    Creates a single, clean, and professional control-style block diagram for Phase 1A.
    This version is ideal for a thesis or presentation, balancing detail with clarity.
    It uses an anchor-based approach for robust and clean connections.
    """
    with schemdraw.Drawing(file='phase1a_final_diagram.svg', show=True, unit=3.5, fontsize=12) as d:
        
        # === 1. DEFINE THE CORE BLOCKS (THE ACTORS) ===
        # We define the main components first. We don't place them yet.
        # This allows us to think about the actors before their positions.
        
        agent_label = 'AGENT PHS\n$z_a = [q_a, p_a]^T$'
        agent_block = dsp.Box(w=4, h=3).label(agent_label).anchor('W')

        market_label = 'MARKET PHS\n$z_m = [q_m, p_m]^T$'
        market_block = dsp.Box(w=4, h=3).label(market_label)

        # === 2. PLACE THE BLOCKS AND DRAW THE MAIN FLOW ===
        # Now, we place the elements relative to each other. This is more robust.
        
        d += agent_block
        d += elm.Line().right(2).at(agent_block.E)
        d += market_block

        # === 3. ADD EXTERNAL INPUTS (NOISE) ===
        # We add the noise input, anchoring it relative to the Market block.
        
        d += (noise_arrow := elm.Arrow().down(1.5).at(market_block.N).label('$w(t)$\n(Stochastic Order Flow)', loc='top'))

        # === 4. DRAW THE COUPLING FEEDBACK PATHS ===
        # This is the key. We draw arrows from the named anchors of one block
        # to the anchors of the other. schemdraw handles the routing.
        
        # Path 1: Market Momentum -> Agent Input
        # We start a line from the 'bottom-right' of the market block.
        # The path attribute '->' draws an arrowhead at the end.
        d += (
            elm.Line()
            .at(market_block.S)
            .down(1.5)
            .label('$p_{market}$', loc='right') # Label the signal
            .dot()
        )
        d += (
            elm.Line()
            .left()
            .tox(agent_block.S)
        )
        d += (
            elm.Arrow()
            .to(agent_block.S)
            .label('$r_{coupling} \\cdot p_{market}$ (Coupling)', loc='bottom')
        )

        # Path 2: Agent Momentum -> Market Input
        # Same logic, but going the other way.
        d += (
            elm.Line()
            .at(agent_block.N)
            .up(1.5)
            .label('$p_{agent}$', loc='left') # Label the signal
            .dot()
        )
        d += (
            elm.Line()
            .right()
            .tox(market_block.N)
        )
        d += (
            elm.Arrow()
            .to(noise_arrow.end) # Connect to the same input point as the noise
            .label('$r_{coupling} \\cdot p_{agent}$ (Coupling)', loc='top')
        )
        
        # === 5. ADD THE "VEIL OF OBSERVATION" ===
        # This visually represents the "grey-box" nature of the problem.
        
        # We find the bounding box of all drawn elements and add a margin
        bbox = d.get_bbox()
        margin = 0.75
        
        # Add a dashed box around everything
        d += elm.Box(
            w=bbox.xmax - bbox.xmin + 2*margin,
            h=bbox.ymax - bbox.ymin + 2*margin
        ).at((bbox.xmin - margin, bbox.ymin - margin)).linestyle('--').color('gray')
        
        d += elm.Label().label('True Unobserved System').at((bbox.centerx, bbox.ymax+margin+0.3)).color('gray')

        # === 6. DRAW THE OBSERVATION OUTPUTS ===
        # These are the only signals that "pierce" the veil.
        
        # Observation of q_agent
        d += (
            elm.Line()
            .at(agent_block.S)
            .toy(bbox.ymin - margin - 1)
            .dot()
        )
        d += (
            elm.Arrow()
            .label('$y_0 = q_{agent}$', loc='bottom')
        )

        # Observation of p_market
        d += (
            elm.Line()
            .at(market_block.S)
            .toy(bbox.ymin - margin - 1)
            .dot()
        )
        d += (
            elm.Arrow()
            .label('$y_1 = p_{market}$', loc='bottom')
        )


if __name__ == "__main__":
    create_final_presentation_diagram()
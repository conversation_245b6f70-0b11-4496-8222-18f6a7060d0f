import schemdraw
import schemdraw.elements as elm

# This script generates a clean and robust visual mockup of the Economic
# Engineering circuit diagram for the linear PHS toy model.
# This version corrects the previous code bug and uses a more standard layout.

with schemdraw.Drawing(file='economic_circuit_diagram_final.svg', show=True) as d:
    d.config(unit=5)

    # --- Start the main loop on the left ---
    start_dot = d.add(elm.Dot())
    
    # The external Alpha Signal u(t), modeled as a voltage source
    d.add(elm.SourceV().up().label('$u(t)$\nAlpha Signal'))
    
    # The top wire represents the price/incentive level p(t)
    d.add(elm.Line().right().label('$p(t)$ (Price/Incentive)', 'top'))
    
    # --- The components that define the system's dynamics ---
    
    # The primary components are arranged in a parallel RLC-like structure.
    
    # Branch 1: Market Impact (Resistor R_p)
    d.push()
    d.add(elm.Resistor().down().label('$R_p$\n(Market Impact)'))
    d.add(elm.Line().to(start_dot.start))
    d.pop()
    
    # Branch 2: Tracking Error (Capacitor C_econ and Target q_ref)
    d.push()
    d.add(elm.Line().right())
    d.add(elm.Capacitor().down().label('$C_{econ} = 1/k$\n(Tracking Error)'))
    d.add(elm.SourceV(polar=True).down().label('$q_{ref}$\n(Target)'))
    d.add(elm.Line().to(start_dot.start))
    d.pop()

    # Branch 3: Market Mass (Inductor L_econ)
    d.push()
    d.add(elm.Line().right().length(10))
    d.add(elm.Inductor().down().label('$L_{econ} = m$\n(Market Mass)'))
    d.add(elm.Line().to(start_dot.start))
    d.pop()

    # --- Add the series trade friction and complete the circuit ---
    d.add(elm.Line().right().length(15))
    
    # Resistor for Trade Friction (R_q)
    d.add(elm.Resistor().down().label('$R_q$\n(Trade Friction)'))
    
    # Ground reference
    d.add(elm.Ground())
    
print("Final circuit diagram generated and saved as 'economic_circuit_diagram_final.svg'")
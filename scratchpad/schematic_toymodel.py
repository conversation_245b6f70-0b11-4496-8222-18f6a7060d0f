import schemdraw
import schemdraw.elements as elm
from schemdraw import dsp

# This script generates a control-style block diagram for the Phase 1A system
# showing two coupled Port-Hamiltonian systems (Agent and Market) with noise
# and partial observations, as described in diagram1_reqs.md

def create_phase1a_diagram():
    """Create the Phase 1A Linear Coupled System diagram"""

    d = schemdraw.Drawing()
    d.config(unit=3, fontsize=12)

    # Title
    d += elm.Label().label('Phase 1A: Linear Coupled PHS System').scale(1.2)
    d += elm.Gap().down(1)

    # === AGENT PHS BLOCK ===
    d.push()

    # Agent system block
    agent_box = d.add(dsp.Box(w=3, h=2).label('AGENT PHS\\n$\\dot{z}_a = (J_a-R_a)M_a z_a$'))

    # Agent outputs
    d += elm.Gap().down(0.5)
    q_agent_dot = d.add(elm.Dot())
    d += elm.Label().label('$q_{agent}$ (observed)', loc='bottom')

    d.pop()

    # === MARKET PHS BLOCK ===
    d.push()
    d += elm.Gap().right(8)

    # Market system block
    market_box = d.add(dsp.Box(w=3, h=2).label('MARKET PHS\\n$\\dot{z}_m = (J_m-R_m)M_m z_m$'))

    # Market outputs
    d += elm.Gap().down(0.5)
    p_market_dot = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$ (observed)', loc='bottom')

    d.pop()

    # === NOISE INPUT ===
    d.push()
    d += elm.Gap().right(8)
    d += elm.Gap().up(2)
    noise_input = d.add(elm.Dot())
    d += elm.Label().label('$w(t)$ (noise)', loc='top')
    d += elm.Line().down(1.5).to(market_box.N)
    d.pop()

    # === COUPLING ===
    # Forward coupling: Agent -> Market
    d += elm.Line().from_(agent_box.E).right(2)
    d += elm.Line().up(1)
    d += elm.Line().right(2)
    d += elm.Line().down(1).to(market_box.W)

    # Backward coupling: Market -> Agent
    d += elm.Line().from_(market_box.W).left(2)
    d += elm.Line().down(1)
    d += elm.Line().left(2)
    d += elm.Line().up(1).to(agent_box.E)

    # Coupling label
    d.push()
    d += elm.Gap().right(4)
    d += elm.Gap().up(0.5)
    d += elm.Label().label('$r_{coupling}$')
    d.pop()

    # Save and show
    d.save('phase1a_system_diagram.svg')
    d.draw()

    return d

def create_detailed_diagram():
    """Create a detailed control-style block diagram showing internal dynamics"""

    d = schemdraw.Drawing()
    d.config(unit=2.5, fontsize=10)

    # Title
    d += elm.Label().label('Phase 1A: Detailed Control Block Diagram').scale(1.3)
    d += elm.Gap().down(1)

    # === AGENT PHS DETAILED BLOCK ===
    d.push()

    # Agent header
    d += elm.Label().label('AGENT PHS').scale(1.1)
    d += elm.Gap().down(0.5)

    # Agent momentum dynamics (top row)
    d.push()
    d += elm.Gap().left(3)

    # Coupling input from market
    coupling_in = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$', loc='left')
    d += elm.Line().right(0.5)

    # Coupling gain
    d += dsp.Box(w=1.5, h=1).label('Coupling\\nGain')
    d += elm.Line().right(0.5)

    # Summation for momentum
    sum_p = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for momentum
    int_p = d.add(dsp.Box(w=1, h=1).label('∫'))
    p_out = d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='right')

    d.pop()

    # Agent position dynamics (bottom row)
    d.push()
    d += elm.Gap().down(2)
    d += elm.Gap().left(1)

    # Summation for position
    sum_q = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for position
    int_q = d.add(dsp.Box(w=1, h=1).label('∫'))
    q_out = d.add(elm.Dot())
    d += elm.Label().label('$q_{agent}$', loc='right')

    d.pop()

    # Agent feedback matrix
    d.push()
    d += elm.Gap().down(1)
    d += elm.Gap().right(2)
    feedback_a = d.add(dsp.Box(w=2, h=1).label('$(J_a-R_a)M_a$'))
    d.pop()

    # Connect agent feedback
    d += elm.Line().from_(q_out).down(1).right(1).to(feedback_a.W)
    d += elm.Line().from_(p_out).down(1).left(1).to(feedback_a.W)
    d += elm.Line().from_(feedback_a.N).up(0.5).left(1).to(sum_p.S)
    d += elm.Line().from_(feedback_a.S).down(0.5).left(1).to(sum_q.N)

    # Connect position to momentum
    d += elm.Line().from_(int_q.E).up(2).to(sum_p.S)

    # Connect momentum to position
    d += elm.Line().from_(int_p.E).down(2).to(sum_q.N)

    d.pop()

    # === MARKET PHS DETAILED BLOCK ===
    d.push()
    d += elm.Gap().right(12)

    # Market header
    d += elm.Label().label('MARKET PHS').scale(1.1)
    d += elm.Gap().down(0.5)

    # Market momentum dynamics (top row)
    d.push()
    d += elm.Gap().left(3)

    # Coupling input from agent
    coupling_in_m = d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='left')
    d += elm.Line().right(0.3)

    # Coupling gain
    d += dsp.Box(w=1.5, h=1).label('Coupling\\nGain')
    d += elm.Line().right(0.3)

    # Noise input
    d.push()
    d += elm.Gap().up(1.5)
    noise_in = d.add(elm.Dot())
    d += elm.Label().label('$w(t)$', loc='top')
    d += elm.Line().down(1.5)
    d.pop()

    # Summation for momentum
    sum_p_m = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for momentum
    int_p_m = d.add(dsp.Box(w=1, h=1).label('∫'))
    p_out_m = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$', loc='right')

    d.pop()

    # Market position dynamics (bottom row)
    d.push()
    d += elm.Gap().down(2)
    d += elm.Gap().left(1)

    # Summation for position
    sum_q_m = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for position
    int_q_m = d.add(dsp.Box(w=1, h=1).label('∫'))
    q_out_m = d.add(elm.Dot())
    d += elm.Label().label('$q_{market}$', loc='right')

    d.pop()

    # Market feedback matrix
    d.push()
    d += elm.Gap().down(1)
    d += elm.Gap().right(2)
    feedback_m = d.add(dsp.Box(w=2, h=1).label('$(J_m-R_m)M_m$'))
    d.pop()

    # Connect market feedback
    d += elm.Line().from_(q_out_m).down(1).right(1).to(feedback_m.W)
    d += elm.Line().from_(p_out_m).down(1).left(1).to(feedback_m.W)
    d += elm.Line().from_(feedback_m.N).up(0.5).left(1).to(sum_p_m.S)
    d += elm.Line().from_(feedback_m.S).down(0.5).left(1).to(sum_q_m.N)

    # Connect position to momentum
    d += elm.Line().from_(int_q_m.E).up(2).to(sum_p_m.S)

    # Connect momentum to position
    d += elm.Line().from_(int_p_m.E).down(2).to(sum_q_m.N)

    d.pop()

    # === COUPLING CONNECTIONS ===
    # Agent to Market coupling
    d += elm.Line().from_(p_out).right(3).down(3).to(coupling_in_m)

    # Market to Agent coupling
    d += elm.Line().from_(p_out_m).left(3).up(3).to(coupling_in)

    # === OBSERVATIONS ===
    d.push()
    d += elm.Gap().down(5)
    d += elm.Gap().right(6)

    d += elm.Label().label('Observations:').scale(1.1)
    d += elm.Gap().down(0.5)

    # Observation lines
    d += elm.Line().from_(q_out).down(4)
    d += elm.Dot()
    d += elm.Label().label('$y[0] = q_{agent}$', loc='bottom')

    d += elm.Line().from_(p_out_m).down(4)
    d += elm.Dot()
    d += elm.Label().label('$y[1] = p_{market}$', loc='bottom')

    d.pop()

    # Save and show
    d.save('phase1a_detailed_diagram.svg')
    d.draw()

    return d

def create_simplified_diagram():
    """Create a simplified version focusing on the key coupling structure"""

    d = schemdraw.Drawing()
    d.config(unit=4, fontsize=12)

    # Title
    d += elm.Label().label('Phase 1A: Coupled PHS Systems').scale(1.5)
    d += elm.Gap().down(1)

    # Agent block
    d.push()
    agent_label = 'AGENT PHS\\n$\\dot{z}_a = (J_a-R_a)M_a z_a + u_{coupling}$'
    agent_block = d.add(dsp.Box(w=4, h=2).label(agent_label))
    d += elm.Gap().down(0.5)
    q_agent_out = d.add(elm.Dot())
    d += elm.Label().label('$q_{agent}$ (observed)', loc='bottom')
    d.pop()

    # Market block
    d.push()
    d += elm.Gap().right(8)
    market_label = 'MARKET PHS\\n$\\dot{z}_m = (J_m-R_m)M_m z_m + u_{coupling} + w(t)$'
    market_block = d.add(dsp.Box(w=4, h=2).label(market_label))
    d += elm.Gap().down(0.5)
    p_market_out = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$ (observed)', loc='bottom')
    d.pop()

    # Noise input
    d.push()
    d += elm.Gap().right(8)
    d += elm.Gap().up(2)
    d += elm.Dot()
    d += elm.Label().label('$w(t)$ (noise)', loc='top')
    d += elm.Line().down(1).to(market_block.N)
    d.pop()

    # Coupling arrows
    d += elm.Line().from_(agent_block.E).right(2)
    d += elm.Line().up(1)
    d += elm.Line().right(2)
    d += elm.Line().down(1).to(market_block.W)

    d += elm.Line().from_(market_block.W).left(2)
    d += elm.Line().down(1)
    d += elm.Line().left(2)
    d += elm.Line().up(1).to(agent_block.E)

    # Coupling label
    d.push()
    d += elm.Gap().right(4)
    d += elm.Gap().up(0.5)
    d += elm.Label().label('$r_{coupling}$')
    d.pop()

    # Save and show
    d.save('phase1a_simplified.svg')
    d.draw()

    return d

if __name__ == "__main__":
    print("Creating Phase 1A system diagrams...")
    create_phase1a_diagram()
    create_detailed_diagram()
    create_simplified_diagram()
    print("Diagrams created:")
    print("1. phase1a_system_diagram.svg - Basic coupled system")
    print("2. phase1a_detailed_diagram.svg - Detailed control-style block diagram")
    print("3. phase1a_simplified.svg - Simplified coupling overview")
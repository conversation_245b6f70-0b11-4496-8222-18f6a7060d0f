import schemdraw
import schemdraw.elements as elm
from schemdraw import dsp

# This script generates a control-style block diagram for the Phase 1A system
# showing two coupled Port-Hamiltonian systems (Agent and Market) with noise
# and partial observations, as described in diagram1_reqs.md

def create_phase1a_diagram():
    """Create a clean Phase 1A system diagram"""

    d = schemdraw.Drawing()
    d.config(unit=4, fontsize=14)

    # Title
    d += elm.Label().label('Phase 1A: Coupled Port-Hamiltonian Systems').scale(1.3)
    d += elm.Gap().down(1.5)

    # === AGENT SYSTEM ===
    d.push()
    agent_box = d.add(dsp.Box(w=4, h=2.5).label('AGENT PHS\\n\\n$\\dot{z}_a = (J_a-R_a)M_a z_a + u_a$\\n\\nwhere $u_a$ = coupling from market'))

    # Agent observation output
    d += elm.Gap().down(1)
    d.add(elm.Dot())
    d += elm.Label().label('$q_{agent}$ (observed)', loc='bottom')
    d.pop()

    # === MARKET SYSTEM ===
    d.push()
    d += elm.Gap().right(10)
    market_box = d.add(dsp.Box(w=4, h=2.5).label('MARKET PHS\\n\\n$\\dot{z}_m = (J_m-R_m)M_m z_m + u_m + w(t)$\\n\\nwhere $u_m$ = coupling from agent'))

    # Market observation output
    d += elm.Gap().down(1)
    d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$ (observed)', loc='bottom')
    d.pop()

    # === NOISE INPUT ===
    d.push()
    d += elm.Gap().right(10)
    d += elm.Gap().up(3)
    d.add(elm.Dot())
    d += elm.Label().label('$w(t)$ (external noise)', loc='top')
    d += elm.Arrow().down(1.5).label('affects $p_{market}$', loc='right')
    d.pop()

    # === COUPLING CONNECTIONS ===
    # Agent momentum -> Market coupling
    d.push()
    d += elm.Gap().right(2)
    d += elm.Gap().up(1)
    d += elm.Arrow().right(6).label('$p_{agent} \\rightarrow r_{coupling} \\cdot p_{agent}$', loc='top')
    d.pop()

    # Market momentum -> Agent coupling
    d.push()
    d += elm.Gap().right(2)
    d += elm.Gap().down(1)
    d += elm.Arrow().left(6).label('$p_{market} \\rightarrow r_{coupling} \\cdot p_{market}$', loc='bottom')
    d.pop()

    # Save and show
    d.save('phase1a_system_diagram.svg')
    d.draw()

    return d

def create_detailed_diagram():
    """Create a detailed control-style block diagram showing internal dynamics"""

    d = schemdraw.Drawing()
    d.config(unit=2.5, fontsize=10)

    # Title
    d += elm.Label().label('Phase 1A: Detailed Control Block Diagram').scale(1.3)
    d += elm.Gap().down(1)

    # === AGENT PHS DETAILED BLOCK ===
    d.push()

    # Agent header
    d += elm.Label().label('AGENT PHS').scale(1.1)
    d += elm.Gap().down(0.5)

    # Agent momentum dynamics (top row)
    d.push()
    d += elm.Gap().left(3)

    # Coupling input from market
    coupling_in = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$', loc='left')
    d += elm.Line().right(0.5)

    # Coupling gain
    d += dsp.Box(w=1.5, h=1).label('Coupling\\nGain')
    d += elm.Line().right(0.5)

    # Summation for momentum
    sum_p = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for momentum
    int_p = d.add(dsp.Box(w=1, h=1).label('∫'))
    p_out = d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='right')

    d.pop()

    # Agent position dynamics (bottom row)
    d.push()
    d += elm.Gap().down(2)
    d += elm.Gap().left(1)

    # Summation for position
    sum_q = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for position
    int_q = d.add(dsp.Box(w=1, h=1).label('∫'))
    q_out = d.add(elm.Dot())
    d += elm.Label().label('$q_{agent}$', loc='right')

    d.pop()

    # Agent feedback matrix
    d.push()
    d += elm.Gap().down(1)
    d += elm.Gap().right(2)
    feedback_a = d.add(dsp.Box(w=2, h=1).label('$(J_a-R_a)M_a$'))
    d.pop()

    # Connect agent feedback
    d += elm.Line().at(q_out).down(1).right(1)
    d += elm.Line().at(p_out).down(1).left(1)
    d += elm.Line().at(feedback_a.N).up(0.5).left(1)
    d += elm.Line().at(feedback_a.S).down(0.5).left(1)

    # Connect position to momentum
    d += elm.Line().at(int_q.E).up(2)

    # Connect momentum to position
    d += elm.Line().at(int_p.E).down(2)

    d.pop()

    # === MARKET PHS DETAILED BLOCK ===
    d.push()
    d += elm.Gap().right(12)

    # Market header
    d += elm.Label().label('MARKET PHS').scale(1.1)
    d += elm.Gap().down(0.5)

    # Market momentum dynamics (top row)
    d.push()
    d += elm.Gap().left(3)

    # Coupling input from agent
    coupling_in_m = d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='left')
    d += elm.Line().right(0.3)

    # Coupling gain
    d += dsp.Box(w=1.5, h=1).label('Coupling\\nGain')
    d += elm.Line().right(0.3)

    # Noise input
    d.push()
    d += elm.Gap().up(1.5)
    noise_in = d.add(elm.Dot())
    d += elm.Label().label('$w(t)$', loc='top')
    d += elm.Line().down(1.5)
    d.pop()

    # Summation for momentum
    sum_p_m = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for momentum
    int_p_m = d.add(dsp.Box(w=1, h=1).label('∫'))
    p_out_m = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$', loc='right')

    d.pop()

    # Market position dynamics (bottom row)
    d.push()
    d += elm.Gap().down(2)
    d += elm.Gap().left(1)

    # Summation for position
    sum_q_m = d.add(dsp.Sum())
    d += elm.Line().right(0.5)

    # Integrator for position
    int_q_m = d.add(dsp.Box(w=1, h=1).label('∫'))
    q_out_m = d.add(elm.Dot())
    d += elm.Label().label('$q_{market}$', loc='right')

    d.pop()

    # Market feedback matrix
    d.push()
    d += elm.Gap().down(1)
    d += elm.Gap().right(2)
    feedback_m = d.add(dsp.Box(w=2, h=1).label('$(J_m-R_m)M_m$'))
    d.pop()

    # Connect market feedback
    d += elm.Line().from_(q_out_m).down(1).right(1).to(feedback_m.W)
    d += elm.Line().from_(p_out_m).down(1).left(1).to(feedback_m.W)
    d += elm.Line().from_(feedback_m.N).up(0.5).left(1).to(sum_p_m.S)
    d += elm.Line().from_(feedback_m.S).down(0.5).left(1).to(sum_q_m.N)

    # Connect position to momentum
    d += elm.Line().from_(int_q_m.E).up(2).to(sum_p_m.S)

    # Connect momentum to position
    d += elm.Line().from_(int_p_m.E).down(2).to(sum_q_m.N)

    d.pop()

    # === COUPLING CONNECTIONS ===
    # Agent to Market coupling
    d += elm.Line().from_(p_out).right(3).down(3).to(coupling_in_m)

    # Market to Agent coupling
    d += elm.Line().from_(p_out_m).left(3).up(3).to(coupling_in)

    # === OBSERVATIONS ===
    d.push()
    d += elm.Gap().down(5)
    d += elm.Gap().right(6)

    d += elm.Label().label('Observations:').scale(1.1)
    d += elm.Gap().down(0.5)

    # Observation lines
    d += elm.Line().from_(q_out).down(4)
    d += elm.Dot()
    d += elm.Label().label('$y[0] = q_{agent}$', loc='bottom')

    d += elm.Line().from_(p_out_m).down(4)
    d += elm.Dot()
    d += elm.Label().label('$y[1] = p_{market}$', loc='bottom')

    d.pop()

    # Save and show
    d.save('phase1a_detailed_diagram.svg')
    d.draw()

    return d

def create_control_block_diagram():
    """Create a proper control block diagram showing the coupling structure"""

    d = schemdraw.Drawing()
    d.config(unit=3, fontsize=12)

    # Title
    d += elm.Label().label('Phase 1A: Control Block Diagram').scale(1.3)
    d += elm.Gap().down(1.5)

    # === AGENT MOMENTUM DYNAMICS ===
    d.push()
    d += elm.Label().label('Agent Momentum Dynamics', loc='top').scale(1.1)
    d += elm.Gap().down(0.5)

    # Coupling input from market
    d.add(elm.Dot())
    d += elm.Label().label('$r_{coupling} \\cdot p_{market}$', loc='left')
    d += elm.Line().right(1)

    # Summation for agent momentum
    sum_agent = d.add(dsp.Sum())
    d += elm.Line().right(1)

    # Integrator for agent momentum
    d += dsp.Box(w=1.5, h=1).label('$\\frac{1}{s}$')
    d += elm.Line().right(1)
    p_agent_out = d.add(elm.Dot())
    d += elm.Label().label('$p_{agent}$', loc='right')
    d.pop()

    # === AGENT POSITION DYNAMICS ===
    d.push()
    d += elm.Gap().down(2.5)
    d += elm.Label().label('Agent Position Dynamics', loc='top').scale(1.1)
    d += elm.Gap().down(0.5)

    # Summation for agent position
    sum_q_agent = d.add(dsp.Sum())
    d += elm.Line().right(1)

    # Integrator for agent position
    d += dsp.Box(w=1.5, h=1).label('$\\frac{1}{s}$')
    d += elm.Line().right(1)
    q_agent_out = d.add(elm.Dot())
    d += elm.Label().label('$q_{agent}$ (observed)', loc='right')
    d.pop()

    # === MARKET MOMENTUM DYNAMICS ===
    d.push()
    d += elm.Gap().right(12)
    d += elm.Label().label('Market Momentum Dynamics', loc='top').scale(1.1)
    d += elm.Gap().down(0.5)

    # Coupling input from agent
    d.add(elm.Dot())
    d += elm.Label().label('$r_{coupling} \\cdot p_{agent}$', loc='left')
    d += elm.Line().right(0.5)

    # Noise summation
    noise_sum = d.add(dsp.Sum())

    # Noise input
    d.push()
    d += elm.Gap().up(1.5)
    d.add(elm.Dot())
    d += elm.Label().label('$w(t)$', loc='top')
    d += elm.Arrow().down(1.5)
    d.pop()

    d += elm.Line().right(0.5)

    # Main summation for market momentum
    sum_market = d.add(dsp.Sum())
    d += elm.Line().right(1)

    # Integrator for market momentum
    d += dsp.Box(w=1.5, h=1).label('$\\frac{1}{s}$')
    d += elm.Line().right(1)
    p_market_out = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$ (observed)', loc='right')
    d.pop()

    # === MARKET POSITION DYNAMICS ===
    d.push()
    d += elm.Gap().right(12)
    d += elm.Gap().down(2.5)
    d += elm.Label().label('Market Position Dynamics', loc='top').scale(1.1)
    d += elm.Gap().down(0.5)

    # Summation for market position
    sum_q_market = d.add(dsp.Sum())
    d += elm.Line().right(1)

    # Integrator for market position
    d += dsp.Box(w=1.5, h=1).label('$\\frac{1}{s}$')
    d += elm.Line().right(1)
    q_market_out = d.add(elm.Dot())
    d += elm.Label().label('$q_{market}$', loc='right')
    d.pop()

    # === FEEDBACK CONNECTIONS ===
    # Agent feedback matrix
    d.push()
    d += elm.Gap().down(5)
    d += elm.Gap().right(3)
    agent_feedback = d.add(dsp.Box(w=3, h=1).label('$(J_a-R_a)M_a$'))
    d.pop()

    # Market feedback matrix
    d.push()
    d += elm.Gap().down(5)
    d += elm.Gap().right(15)
    market_feedback = d.add(dsp.Box(w=3, h=1).label('$(J_m-R_m)M_m$'))
    d.pop()

    # === COUPLING ARROWS ===
    # Agent momentum to market
    d.push()
    d += elm.Gap().right(6)
    d += elm.Gap().up(2)
    d += elm.Arrow().right(6).label('momentum coupling', loc='top')
    d.pop()

    # Market momentum to agent
    d.push()
    d += elm.Gap().right(6)
    d += elm.Gap().down(2)
    d += elm.Arrow().left(6).label('momentum coupling', loc='bottom')
    d.pop()

    # Save and show
    d.save('phase1a_control_diagram.svg')
    d.draw()

    return d

def create_simplified_diagram():
    """Create a simplified version focusing on the key coupling structure"""

    d = schemdraw.Drawing()
    d.config(unit=4, fontsize=12)

    # Title
    d += elm.Label().label('Phase 1A: Coupled PHS Systems').scale(1.5)
    d += elm.Gap().down(1)

    # Agent block
    d.push()
    agent_label = 'AGENT PHS\\n$\\dot{z}_a = (J_a-R_a)M_a z_a + u_{coupling}$'
    agent_block = d.add(dsp.Box(w=4, h=2).label(agent_label))
    d += elm.Gap().down(0.5)
    q_agent_out = d.add(elm.Dot())
    d += elm.Label().label('$q_{agent}$ (observed)', loc='bottom')
    d.pop()

    # Market block
    d.push()
    d += elm.Gap().right(8)
    market_label = 'MARKET PHS\\n$\\dot{z}_m = (J_m-R_m)M_m z_m + u_{coupling} + w(t)$'
    market_block = d.add(dsp.Box(w=4, h=2).label(market_label))
    d += elm.Gap().down(0.5)
    p_market_out = d.add(elm.Dot())
    d += elm.Label().label('$p_{market}$ (observed)', loc='bottom')
    d.pop()

    # Noise input
    d.push()
    d += elm.Gap().right(8)
    d += elm.Gap().up(2)
    d += elm.Dot()
    d += elm.Label().label('$w(t)$ (noise)', loc='top')
    d += elm.Line().down(1).to(market_block.N)
    d.pop()

    # Coupling arrows
    d.push()
    d += elm.Gap().right(4)
    d += elm.Gap().up(0.5)
    d += elm.Arrow().right(2).label('$r_{coupling}$', loc='top')
    d.pop()

    d.push()
    d += elm.Gap().right(4)
    d += elm.Gap().down(0.5)
    d += elm.Arrow().left(2).label('$r_{coupling}$', loc='bottom')
    d.pop()



    # Save and show
    d.save('phase1a_simplified.svg')
    d.draw()

    return d

if __name__ == "__main__":
    print("Creating Phase 1A system diagrams...")
    create_phase1a_diagram()
    create_control_block_diagram()
    create_simplified_diagram()
    print("Diagrams created:")
    print("1. phase1a_system_diagram.svg - Basic coupled system")
    print("2. phase1a_control_diagram.svg - Control block diagram with integrators")
    print("3. phase1a_simplified.svg - Simplified coupling overview")
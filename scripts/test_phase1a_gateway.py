#!/usr/bin/env python3
"""
Test script for Phase 1A Linear De-coupling Gateway.

This script demonstrates the complete Phase 1A symbolic analysis pipeline:
1. Initialize the coupled agent-market system
2. Run theoretical gateway validation
3. Generate and save comprehensive report

Usage:
    python scripts/test_phase1a_gateway.py
"""

import sys
import os
from pathlib import Path
import jax.numpy as jnp

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from phynance.synthetic.phase1a_system import Phase1ACoupledSystem, Phase1AParameters


def main():
    """Run Phase 1A gateway analysis and generate report."""
    print("=" * 80)
    print("Phase 1A Linear De-coupling Gateway Test")
    print("Testing symbolic identifiability analysis")
    print("=" * 80)
    
    # Initialize Phase 1A system
    print("Initializing Phase 1A coupled agent-market system...")
    system = Phase1ACoupledSystem()
    
    # Define test parameters
    test_params = Phase1AParameters(
        # Agent parameters (target for identification)
        k_agent=2.5,
        m_agent=1.2,
        r_agent=0.15,
        
        # Market parameters  
        k_market=1.8,
        m_market=0.9,
        r_market=0.25,
        
        # Coupling parameters
        r_coupling=0.08,
        
        # Noise parameters
        sigma_market=0.12,
    )
    
    print(f"Agent parameters: k={test_params.k_agent}, m={test_params.m_agent}, r={test_params.r_agent}")
    print(f"Market parameters: k={test_params.k_market}, m={test_params.m_market}, r={test_params.r_market}")
    print(f"Coupling strength: r_coupling={test_params.r_coupling}")
    print()
    
    # Convert to PHS parameters
    phs_params = test_params.to_phs_parameters()
    
    # Run complete gateway analysis
    try:
        results = system.run_gateway_analysis(phs_params)
        
        # Save detailed report to file
        output_dir = Path("docs/phase1a_results")
        output_dir.mkdir(exist_ok=True)
        
        # Generate and save comprehensive report
        report_path = output_dir / "phase1a_gateway_report.md"
        
        with open(report_path, "w") as f:
            f.write("# Phase 1A Linear De-coupling Gateway Analysis Report\n\n")
            f.write(f"Generated on: {os.popen('date').read().strip()}\n\n")
            
            f.write("## System Configuration\n\n")
            f.write("### Parameters Used\n\n")
            f.write("**Agent Parameters** (Target for Identification):\n")
            f.write(f"- $k_{{\\text{{agent}}}} = {test_params.k_agent}$ (stiffness)\n")
            f.write(f"- $m_{{\\text{{agent}}}} = {test_params.m_agent}$ (mass)\n")
            f.write(f"- $r_{{\\text{{agent}}}} = {test_params.r_agent}$ (resistance)\n\n")
            f.write("**Market Parameters:**\n")
            f.write(f"- $k_{{\\text{{market}}}} = {test_params.k_market}$ (stiffness)\n")
            f.write(f"- $m_{{\\text{{market}}}} = {test_params.m_market}$ (mass)\n")
            f.write(f"- $r_{{\\text{{market}}}} = {test_params.r_market}$ (resistance)\n\n")
            f.write("**Coupling Parameters:**\n")
            f.write(f"- $r_{{\\text{{coupling}}}} = {test_params.r_coupling}$ (bidirectional coupling strength)\n")
            f.write(f"- $\\sigma_{{\\text{{market}}}} = {test_params.sigma_market}$ (external noise intensity)\n\n")
            
            f.write("### Port-Hamiltonian System Formulation\n\n")
            f.write("**System Dynamics**:\n")
            f.write("$$\\frac{d\\mathbf{z}}{dt} = (\\mathbf{J} - \\mathbf{R}(\\boldsymbol{\\theta})) \\nabla H(\\mathbf{z}) + \\mathbf{E} d\\mathbf{W}(t)$$\n\n")
            f.write("where $\\nabla H(\\mathbf{z}) = \\frac{\\partial H}{\\partial \\mathbf{z}}$ is the Hamiltonian gradient.\n\n")
            f.write("**State Vector**:\n")
            f.write("$$\\mathbf{z} = \\begin{bmatrix} q_{\\text{agent}} \\\\ p_{\\text{agent}} \\\\ q_{\\text{market}} \\\\ p_{\\text{market}} \\end{bmatrix}$$\n\n")
            f.write("**Canonical Symplectic Matrix**:\n")
            f.write("$$\\mathbf{J} = \\begin{bmatrix} 0 & 1 & 0 & 0 \\\\ -1 & 0 & 0 & 0 \\\\ 0 & 0 & 0 & 1 \\\\ 0 & 0 & -1 & 0 \\end{bmatrix}$$\n\n")
            f.write("**Hamiltonian Matrix**:\n")
            f.write("$$\\mathbf{M}(\\boldsymbol{\\theta}) = \\begin{bmatrix} k_{\\text{agent}} & 0 & 0 & 0 \\\\ 0 & \\frac{1}{m_{\\text{agent}}} & 0 & 0 \\\\ 0 & 0 & k_{\\text{market}} & 0 \\\\ 0 & 0 & 0 & \\frac{1}{m_{\\text{market}}} \\end{bmatrix}$$\n\n")
            f.write("**Dissipation Matrix**:\n")
            f.write("$$\\mathbf{R}(\\boldsymbol{\\theta}) = \\begin{bmatrix} 0 & 0 & 0 & 0 \\\\ 0 & r_{\\text{agent}} & 0 & r_{\\text{coupling}} \\\\ 0 & 0 & 0 & 0 \\\\ 0 & r_{\\text{coupling}} & 0 & r_{\\text{market}} \\end{bmatrix}$$\n\n")
            f.write("**Noise Coupling Matrix**:\n")
            f.write("$$\\mathbf{E} = \\begin{bmatrix} 0 \\\\ 0 \\\\ 0 \\\\ \\sigma_{\\text{market}} \\end{bmatrix}$$\n\n")
            f.write("### System Properties\n\n")
            f.write("- **Observation Model**: $\\mathbf{y} = \\mathbf{C}\\mathbf{z} = \\begin{bmatrix} q_{\\text{agent}} \\\\ p_{\\text{market}} \\end{bmatrix}$ (agent position, market momentum)\n")
            f.write("- **Noise Model**: External order flow excites $p_{\\text{market}}$ only\n")
            f.write("- **Coupling Mechanism**: Bidirectional momentum exchange via $r_{\\text{coupling}}$\n")
            f.write("- **Hamiltonian**: $H(\\mathbf{z}) = \\frac{1}{2}\\left[k_{\\text{agent}}q_{\\text{agent}}^2 + \\frac{p_{\\text{agent}}^2}{m_{\\text{agent}}} + k_{\\text{market}}q_{\\text{market}}^2 + \\frac{p_{\\text{market}}^2}{m_{\\text{market}}}\\right]$\n")
            f.write("- **Hamiltonian Gradient**: $\\nabla H(\\mathbf{z}) = \\mathbf{M}(\\boldsymbol{\\theta}) \\mathbf{z}$ for quadratic $H$\n\n")
            
            # Add gateway report
            f.write("## Gateway Analysis Results\n\n")
            f.write("```\n")
            f.write(system.analyzer.generate_gateway_report())
            f.write("```\n\n")
            
            # Add detailed analysis results
            f.write("## Detailed Analysis Results\n\n")
            
            # Observability results
            obs_results = results.get('observability', {})
            f.write("### Observability Analysis\n\n")
            f.write("**Kalman Rank Condition**: Testing if all internal states can be inferred from observations.\n\n")
            shape = obs_results.get('observability_matrix_shape', ('?', '?'))
            f.write(f"- **Observability Matrix**: $\\mathcal{{O}} \\in \\mathbb{{R}}^{{{shape[0]} \\\\times {shape[1]}}}$\n")
            f.write(f"- **Rank**: $\\text{{rank}}(\\mathcal{{O}}) = {obs_results.get('rank', 'N/A')}$ (required: ${obs_results.get('required_rank', 'N/A')}$)\n")
            f.write(f"- **Result**: {'✅ **System is Observable**' if obs_results.get('is_observable', False) else '❌ **System is NOT Observable**'}\n")
            f.write(f"- **Observation Vector**: $\\mathbf{{y}} = [{', '.join([f'{state}' for state in obs_results.get('observed_states', [])])}]^T$\n")
            if obs_results.get('unobservable_states'):
                f.write(f"- **Unobservable States**: {obs_results.get('unobservable_states', 'N/A')}\n")
            
            # Add mathematical matrices
            matrices = obs_results.get('matrices', {})
            if matrices:
                f.write("\n#### Mathematical System Matrices\n\n")
                f.write("**Observation Matrix** $\\mathbf{C}$:\n")
                f.write(f"{matrices.get('C_latex', 'Not available')}\n\n")
                f.write("**Linear State Matrix** $\\mathbf{A} = (\\mathbf{J} - \\mathbf{R})\\mathbf{M}$ (since $\\nabla H = \\mathbf{M}\\mathbf{z}$):\n") 
                f.write(f"{matrices.get('A_latex', 'Not available')}\n\n")
                f.write("**Observability Matrix** $\\mathcal{O} = [\\mathbf{C}^T, (\\mathbf{CA})^T, (\\mathbf{CA}^2)^T, \\ldots, (\\mathbf{CA}^{n-1})^T]^T$:\n")
                f.write(f"{matrices.get('O_latex', 'Not available')}\n\n")
            f.write("\n")
            
            # Controllability results  
            ctrl_results = results.get('controllability', {})
            f.write("### Controllability Analysis\n")
            f.write(f"- **Matrix Shape**: {ctrl_results.get('noise_coupling_matrix_shape', 'N/A')}\n")
            f.write(f"- **Rank**: {ctrl_results.get('rank', 'N/A')}/{ctrl_results.get('required_rank', 'N/A')}\n")
            f.write(f"- **Controllable**: {'✅ Yes' if ctrl_results.get('is_controllable', False) else '❌ No'}\n")
            f.write(f"- **Noise Targets**: {ctrl_results.get('noise_targets', 'N/A')}\n")
            
            excitation = ctrl_results.get('excitation_analysis', {})
            if excitation:
                f.write(f"- **Directly Excited States**: {excitation.get('directly_excited_states', 'N/A')}\n")
                f.write(f"- **Unexcited States**: {excitation.get('unexcited_states', 'N/A')}\n")
            f.write("\n")
            
            # Identifiability results
            ident_results = results.get('identifiability', {})
            f.write("### Structural Identifiability Analysis\n")
            f.write(f"- **Transfer Function Shape**: {ident_results.get('transfer_function_shape', 'N/A')}\n")
            f.write(f"- **Total Parameters**: {ident_results.get('all_parameters', 'N/A')}\n")
            f.write(f"- **Agent Parameters**: {ident_results.get('agent_parameters', 'N/A')}\n")
            f.write(f"- **Coefficients Found**: {ident_results.get('coefficients_found', 'N/A')}\n")
            f.write(f"- **Globally Identifiable**: {'✅ Yes' if ident_results.get('globally_identifiable', False) else '❌ No'}\n")
            
            agent_focus = ident_results.get('agent_parameter_focus', {})
            if agent_focus:
                f.write(f"- **Agent Parameter Count**: {agent_focus.get('agent_parameter_count', 'N/A')}\n")
                f.write(f"- **Agent Parameters**: {agent_focus.get('agent_parameters', 'N/A')}\n")
            
            # Add transfer function matrix
            ident_matrices = ident_results.get('matrices', {})
            if ident_matrices:
                f.write("\n#### Transfer Function Matrix\n\n")
                f.write("**Transfer Function G(s):**\n")
                f.write(f"{ident_matrices.get('G_latex', 'Not available')}\n\n")
                f.write("This transfer function represents the input-output relationship ")
                f.write("G(s) = C(sI - A)^(-1), which is analyzed for structural identifiability.\n")
            f.write("\n")
            
            # Constraints results
            constraints_results = results.get('constraints', {})
            f.write("### Parameter Constraints Analysis\n")
            f.write(f"- **Total Constraints**: {constraints_results.get('total_constraints', 'N/A')}\n")
            
            pos_constraints = constraints_results.get('positivity_constraints', [])
            if pos_constraints:
                f.write("- **Positivity Constraints**:\n")
                for constraint in pos_constraints[:5]:  # Show first 5
                    f.write(f"  - {constraint}\n")
                if len(pos_constraints) > 5:
                    f.write(f"  - ... and {len(pos_constraints) - 5} more\n")
            
            semidef_constraints = constraints_results.get('semidefinite_constraints', [])
            if semidef_constraints:
                f.write("- **Semidefinite Constraints**:\n")
                for constraint in semidef_constraints[:3]:  # Show first 3
                    f.write(f"  - {constraint}\n")
                if len(semidef_constraints) > 3:
                    f.write(f"  - ... and {len(semidef_constraints) - 3} more\n")
            f.write("\n")
            
            # Summary
            f.write("## Executive Summary\n\n")
            gateway_passed = results.get('gateway_passed', False)
            f.write(f"**Gateway Status**: {'✅ PASSED' if gateway_passed else '❌ FAILED'}\n\n")
            
            if gateway_passed:
                f.write("### ✅ Success! Key Findings:\n")
                f.write("- All theoretical conditions for parameter identification are satisfied\n")
                f.write("- The system is observable from position measurements alone\n")
                f.write("- Market noise provides sufficient excitation for identification\n") 
                f.write("- Agent parameters are structurally identifiable despite coupling\n")
                f.write("- **Ready to proceed with numerical PEM identification**\n\n")
                
                f.write("### Next Steps:\n")
                f.write("1. Generate synthetic trajectory data using stochastic PHS simulation\n")
                f.write("2. Implement PEM identifier with Kalman filtering\n")
                f.write("3. Validate parameter recovery against ground truth\n")
                f.write("4. Proceed to Phase 1B if identification succeeds\n\n")
            else:
                f.write("### ❌ Gateway Failed - Issues to Address:\n")
                recommendations = results.get('recommendations', [])
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec}\n")
                f.write("\n")
                f.write("**Do not proceed to numerical identification until these issues are resolved.**\n\n")
            
            # Technical appendix
            f.write("## Technical Appendix\n\n")
            f.write("### Mathematical Foundation\n")
            f.write("This analysis implements the Phase 1A Linear De-coupling Gateway as described in:\n")
            f.write("*The Structure of Strategy: A First-Principles Approach to Discovering the Dynamics of Active Economic Agents*\n\n")
            
            f.write("The gateway validates two critical mathematical conditions:\n\n")
            f.write("1. **Observability (Kalman Rank Condition)**:\n")
            f.write("   - Constructs observability matrix O = [C; CA; CA²; ...; CA^(n-1)]\n")
            f.write("   - Verifies rank(O) = n for complete state reconstruction\n")
            f.write("   - Tests if internal momentum p_agent can be inferred from positions\n\n")
            
            f.write("2. **Structural Identifiability (Transfer Function Analysis)**:\n") 
            f.write("   - Derives transfer function G(s,θ) = C(sI - A(θ))^(-1)\n")
            f.write("   - Extracts polynomial coefficients as functions of parameters\n")
            f.write("   - Verifies parameter-to-coefficient mapping is one-to-one\n")
            f.write("   - Ensures agent parameters can be distinguished from market parameters\n\n")
            
            f.write("### System Architecture\n")
            f.write("```\n")
            f.write("State Vector: z = [q_agent, p_agent, q_market, p_market]^T\n")
            f.write("\n")
            f.write("Dynamics: dz/dt = (J - R(θ)) * M(θ) * z + E * dW(t)\n")
            f.write("\n")
            f.write("Where:\n")
            f.write("- J: Canonical symplectic matrix (4×4)\n")
            f.write("- R(θ): Dissipation matrix with bidirectional coupling\n")
            f.write("- M(θ): Hamiltonian matrix = diag([k_a, 1/m_a, k_m, 1/m_m])\n")
            f.write("- E: Noise coupling matrix (noise only on p_market)\n")
            f.write("- dW(t): External order flow (market noise)\n")
            f.write("```\n\n")
            
            f.write("This analysis provides the mathematical foundation for the entire thesis, ")
            f.write("validating that agent parameters can be recovered even in the most ")
            f.write("challenging scenario with bidirectional agent-market coupling.\n")

        print(f"✅ Comprehensive report saved to: {report_path}")
        
        # Also save a summary report
        summary_path = output_dir / "phase1a_gateway_summary.txt"
        with open(summary_path, "w") as f:
            f.write("PHASE 1A LINEAR DE-COUPLING GATEWAY - SUMMARY REPORT\n")
            f.write("=" * 60 + "\n\n")
            
            gateway_passed = results.get('gateway_passed', False)
            f.write(f"GATEWAY STATUS: {'PASSED ✅' if gateway_passed else 'FAILED ❌'}\n\n")
            
            # Quick status overview
            summary = results.get('summary', {})
            f.write("CONDITION CHECKS:\n")
            f.write(f"- Observability:     {summary.get('observability_check', 'UNKNOWN')}\n")
            f.write(f"- Controllability:   {summary.get('controllability_check', 'UNKNOWN')}\n")  
            f.write(f"- Identifiability:   {summary.get('identifiability_check', 'UNKNOWN')}\n")
            f.write(f"- Overall Gateway:   {summary.get('overall_gateway', 'UNKNOWN')}\n\n")
            
            f.write("SYSTEM CONFIGURATION:\n")
            f.write(f"- States: 4 (q_agent, p_agent, q_market, p_market)\n")
            f.write(f"- Parameters: 7 (3 agent + 3 market + 1 coupling)\n")
            f.write(f"- Observations: 2 (q_agent, p_market)\n") 
            f.write(f"- Noise: 1 source (external order flow → p_market)\n\n")
            
            if gateway_passed:
                f.write("STATUS: Ready for numerical PEM identification ✅\n")
                f.write("NEXT: Generate synthetic data and validate parameter recovery\n")
            else:
                f.write("STATUS: Theoretical conditions not met ❌\n")
                f.write("ACTION: Address identifiability issues before proceeding\n")
        
        print(f"📋 Summary report saved to: {summary_path}")
        
        # Test system eigenvalues for stability
        print("\n" + "=" * 40)
        print("Additional System Analysis")
        print("=" * 40)
        
        eigenvals = system.get_system_eigenvalues(phs_params)
        print(f"System eigenvalues: {eigenvals}")
        
        max_real = jnp.max(jnp.real(eigenvals))
        if max_real < 0:
            print("✅ System is stable (all eigenvalues have negative real parts)")
        else:
            print("⚠️  System may be unstable (some eigenvalues have positive real parts)")
        
        print(f"\nAll results saved to: {output_dir}")
        print("\n" + "=" * 80)
        if gateway_passed:
            print("🎉 Phase 1A Gateway PASSED - Ready for numerical validation!")
        else:
            print("⚠️  Phase 1A Gateway FAILED - Review recommendations above")
        print("=" * 80)
        
        return results
        
    except Exception as e:
        print(f"❌ Error during gateway analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    results = main()
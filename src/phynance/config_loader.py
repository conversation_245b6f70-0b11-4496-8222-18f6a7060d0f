# Placeholder for config loader logic
# This script will be responsible for loading configuration files (e.g., YAML or TOML)
# from the 'config/' directory.

import yaml
import toml
import os

CONFIG_DIR = os.path.join(os.path.dirname(__file__), '..', '..', 'config') # Relative path to config directory

def load_config(config_filename):
    """
    Loads a configuration file from the config directory.
    The file type is inferred from the extension (.yaml, .yml, or .toml).

    Args:
        config_filename (str): The name of the configuration file (e.g., 'global_settings.yaml').

    Returns:
        dict: The loaded configuration.

    Raises:
        FileNotFoundError: If the config file is not found.
        ValueError: If the file extension is not supported or if there's a parsing error.
    """
    config_path = os.path.join(CONFIG_DIR, config_filename)

    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    _, ext = os.path.splitext(config_filename)

    try:
        with open(config_path, 'r') as f:
            if ext in ['.yaml', '.yml']:
                return yaml.safe_load(f)
            elif ext == '.toml':
                return toml.load(f)
            else:
                raise ValueError(f"Unsupported configuration file extension: {ext}. Please use .yaml, .yml, or .toml.")
    except yaml.YAMLError as e:
        raise ValueError(f"Error parsing YAML file {config_filename}: {e}")
    except toml.TomlDecodeError as e:
        raise ValueError(f"Error parsing TOML file {config_filename}: {e}")
    except Exception as e:
        raise ValueError(f"An unexpected error occurred while loading {config_filename}: {e}")

if __name__ == '__main__':
    # Example usage (assuming you have a 'global_settings.yaml' in 'config/')
    # Create a dummy config file for testing
    dummy_config_path = os.path.join(CONFIG_DIR, 'dummy_test_config.yaml')
    if not os.path.exists(CONFIG_DIR):
        os.makedirs(CONFIG_DIR)
    with open(dummy_config_path, 'w') as f:
        yaml.dump({'setting1': 'value1', 'feature_flag': True}, f)

    try:
        print(f"Attempting to load: {dummy_config_path}")
        config = load_config('dummy_test_config.yaml')
        print("Successfully loaded dummy_test_config.yaml:")
        print(config)
    except Exception as e:
        print(f"Error loading dummy config: {e}")

    # Clean up dummy file
    # os.remove(dummy_config_path)
    print(f"\nNote: You might need to create actual configuration files in the '{CONFIG_DIR}' directory.")

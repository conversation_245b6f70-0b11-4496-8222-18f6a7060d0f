"""
Core Port-Hamiltonian System components shared across all phases.

This module provides the fundamental PHS building blocks that are reused
throughout Phase 1A (Linear De-coupling Gateway), Phase 1B (nonlinear discovery validation), 
and Phase 2 (real ARKK data).

Phase 1A Focus: Symbolic foundations for observability/identifiability analysis
and stochastic PHS formulation with Market Maker price noise.
"""

from .phs_base import PHSBase, PHSParameters
from .state_variables import StateVariables
from .hamiltonian_base import HamiltonianBase, SymbolicQuadraticHamiltonian, QuadraticHamiltonian
from .dissipation_base import DissipationBase, SymbolicDissipationMatrix, CoupledDissipation
from .coupling_base import CouplingBase
from .ode_integrators import integrate_phs

__all__ = [
    "PHSBase",
    "PHSParameters",
    "StateVariables", 
    "HamiltonianBase",
    "SymbolicQuadraticHamiltonian",
    "QuadraticHamiltonian",
    "DissipationBase",
    "SymbolicDissipationMatrix", 
    "CoupledDissipation",
    "CouplingBase",
    "integrate_phs"
]
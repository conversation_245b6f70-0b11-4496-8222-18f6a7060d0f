"""
Base classes for coupling matrices g(q,p) in PHS systems.

The coupling matrix g defines how external inputs u(t) influence the system.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict

import jax.numpy as jnp


class CouplingBase(ABC):
    """
    Abstract base class for coupling matrices g(q,p).

    Different phases use different implementations:
    - Phase 1A: Constant coupling matrix for linear systems
    - Phase 1B: Known symbolic coupling for validation
    - Phase 2: Discovered symbolic coupling via PySR
    """

    @abstractmethod
    def matrix(self, z: jnp.ndarray, params: Dict[str, Any]) -> jnp.ndarray:
        """Compute coupling matrix g(z)."""
        pass

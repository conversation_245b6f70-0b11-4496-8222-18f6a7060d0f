"""
Base classes for dissipation matrices R(q,p) in PHS systems.

The dissipation matrix R represents frictions, transaction costs, and energy dissipation.
Must be positive semi-definite to ensure energy dissipation (never creation).
"""

import jax
import jax.numpy as jnp
import equinox as eqx
from abc import abstractmethod
import sympy as sp


class DissipationBase(eqx.Module):
    """
    Abstract base class for dissipation matrices R(q,p).

    Different phases use different implementations:
    - Phase 1A: Constant dissipation matrix for linear systems
    - Phase 1B: Known symbolic dissipation for validation
    - Phase 2: Discovered symbolic dissipation via PySR
    """

    @abstractmethod
    def matrix(self, z: jnp.ndarray | None = None) -> jnp.ndarray:
        """Compute dissipation matrix R(z)."""
        pass

    def is_positive_semidefinite(self, z: jnp.ndarray | None = None) -> bool:
        """
        Check if R(z) is positive semi-definite.

        Default implementation computes eigenvalues and checks if all >= 0.
        Subclasses can override for more efficient checks when structure is known.
        """
        R = self.matrix(z)
        eigenvals = jnp.linalg.eigvals(R)
        return bool(jnp.all(eigenvals >= 0))


class SymbolicDissipationMatrix(eqx.Module):
    """
    Symbolic dissipation matrix R_total(θ) for Phase 1A Linear De-coupling Gateway.
    
    Includes bidirectional coupling between agent and Market Maker subsystems,
    creating the most challenging identifiability scenario for gateway validation.
    """

    state_names: list = eqx.field(static=True)
    param_names: list = eqx.field(static=True)
    z_sym: tuple = eqx.field(static=True)
    params_sym: tuple = eqx.field(static=True)
    R_symbolic: sp.Matrix = eqx.field(static=True)

    def __init__(self):
        self.state_names = ["q_agent", "p_agent", "q_market", "p_market"]
        self.param_names = ["r_agent", "r_market", "r_coupling"]
        self.z_sym = sp.symbols(self.state_names)
        self.params_sym = sp.symbols(self.param_names)
        r_a, r_m, r_c = self.params_sym

        self.R_symbolic = sp.Matrix(
            [[0, 0, 0, 0], [0, r_a, 0, r_c], [0, 0, 0, 0], [0, r_c, 0, r_m]]
        )

    def to_numeric(self, r_agent, r_market, r_coupling):
        return CoupledDissipation(
            r_agent=jnp.asarray(r_agent),
            r_market=jnp.asarray(r_market),
            r_coupling=jnp.asarray(r_coupling),
        )

    @property
    def symbolic_matrix(self) -> sp.Matrix:
        return self.R_symbolic


class CoupledDissipation(DissipationBase):
    """Numerical coupled dissipation matrix for Phase 1A."""

    r_agent: jax.Array
    r_market: jax.Array
    r_coupling: jax.Array

    def __init__(self, r_agent, r_market, r_coupling):
        self.r_agent = jnp.asarray(r_agent)
        self.r_market = jnp.asarray(r_market)
        self.r_coupling = jnp.asarray(r_coupling)

    def matrix(self, z: jnp.ndarray | None = None) -> jnp.ndarray:
        return jnp.array(
            [
                [0.0, 0.0, 0.0, 0.0],
                [0.0, self.r_agent, 0.0, self.r_coupling],
                [0.0, 0.0, 0.0, 0.0],
                [0.0, self.r_coupling, 0.0, self.r_market],
            ]
        )

    def is_positive_semidefinite(self, z: jnp.ndarray | None = None) -> bool:
        return bool(
            jnp.all(jnp.array([self.r_agent, self.r_market]) >= 0)
            & (self.r_agent * self.r_market - self.r_coupling**2 >= 0)
        )


class PolynomialDissipation(DissipationBase):
    """Polynomial dissipation matrix for nonlinear systems (Phase 1B)."""

    def matrix(self, z: jnp.ndarray | None = None) -> jnp.ndarray:
        raise NotImplementedError

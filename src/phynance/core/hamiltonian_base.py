"""
Base classes for Hamiltonian functions in PHS systems.

The Hamiltonian H(q,p) represents the "Economic Surplus" or total energy
of the system, capturing the ETF's strategic objective function.
"""

import jax
import jax.numpy as jnp
import equinox as eqx
from abc import abstractmethod
import sympy as sp


class HamiltonianBase(eqx.Module):
    """
    Abstract base class for Hamiltonian functions H(q,p).

    The Hamiltonian represents the total energy or "Economic Surplus"
    of the system. Different phases use different implementations:
    - Phase 1A: Quadratic Hamiltonian for linear systems
    - Phase 1B: Known polynomial Hamiltonian for validation
    - Phase 2: Learned Hamiltonian via HNN

    Parameters are stored as individual attributes for clean JAX integration.
    """

    @abstractmethod
    def value(self, z: jnp.ndarray) -> jnp.ndarray:
        """Compute Hamiltonian value H(z)."""
        pass

    def gradient(self, z: jnp.ndarray) -> jnp.ndarray:
        """
        Compute Hamiltonian gradient ∇H(z) via automatic differentiation.

        Default implementation uses JAX. Subclasses can override for efficiency
        when analytical forms are available (e.g., quadratic Hamiltonians).
        """
        return jax.grad(self.value)(z)

    def hessian(self, z: jnp.ndarray) -> jnp.ndarray:
        """
        Compute Hamiltonian Hessian ∇²H(z) via automatic differentiation.

        Default implementation uses JAX. Subclasses can override for efficiency
        when analytical forms are available (e.g., quadratic Hamiltonians).
        """
        return jax.hessian(self.value)(z)

    def is_convex(self, z: jnp.ndarray) -> bool:
        """
        Check convexity by examining Hessian eigenvalues.

        Default implementation computes Hessian and checks if all eigenvalues >= 0.
        Subclasses can override for more efficient checks when structure is known.
        """
        H = self.hessian(z)
        eigenvals = jnp.linalg.eigvals(H)
        return bool(jnp.all(eigenvals >= 0))


class SymbolicQuadraticHamiltonian(eqx.Module):
    """
    Pure symbolic quadratic Hamiltonian H(z) = 0.5 * z^T * M * z for Phase 1A Linear De-coupling Gateway.

    White-box coupled agent-Market Maker system:
    z_total = [q_agent, p_agent, q_market, p_market]
    M_total = diag([k_agent, 1/m_agent, k_market, 1/m_market])

    Provides symbolic expressions for Phase 1A theoretical gateway validation:
    - Observability analysis via symbolic rank computation
    - Identifiability analysis via transfer function coefficients
    """

    # All fields are static as this class contains no trainable parameters
    state_names: list = eqx.field(static=True)
    param_names: list = eqx.field(static=True)
    z_sym: tuple = eqx.field(static=True)
    params_sym: tuple = eqx.field(static=True)
    M_symbolic: sp.Matrix = eqx.field(static=True)

    def __init__(self):
        """
        Initialize symbolic Phase 1A Hamiltonian structure.

        Creates symbolic expressions for identifiability analysis.
        """
        # State and parameter names for Phase 1A (static)
        self.state_names = ["q_agent", "p_agent", "q_market", "p_market"]
        self.param_names = ["k_agent", "m_agent", "k_market", "m_market"]

        # Store symbolic expressions for identifiability analysis (static)
        self.z_sym = sp.symbols(self.state_names)
        self.params_sym = sp.symbols(self.param_names)

        # Build symbolic M matrix: diag([k_agent, 1/m_agent, k_market, 1/m_market])
        k_a, m_a, k_m, m_m = self.params_sym
        self.M_symbolic = sp.diag(k_a, 1 / m_a, k_m, 1 / m_m)

    def to_numeric(
        self, k_agent, m_agent, k_market, m_market
    ) -> "QuadraticHamiltonian":
        """
        Instantiates a numerical QuadraticHamiltonian with the given parameter values.

        Args:
            k_agent: Agent stiffness parameter
            m_agent: Agent mass parameter
            k_market: Market stiffness parameter
            m_market: Market mass parameter

        Returns:
            QuadraticHamiltonian instance for numerical simulation
        """
        return QuadraticHamiltonian(
            k_agent=jnp.asarray(k_agent, dtype=jnp.float32),
            m_agent=jnp.asarray(m_agent, dtype=jnp.float32),
            k_market=jnp.asarray(k_market, dtype=jnp.float32),
            m_market=jnp.asarray(m_market, dtype=jnp.float32),
        )

    # Symbolic access for identifiability analysis
    @property
    def symbolic_matrix(self) -> sp.Matrix:
        """Symbolic M matrix."""
        return self.M_symbolic

    @property
    def symbolic_hamiltonian(self) -> sp.Expr:
        """Symbolic Hamiltonian expression."""
        z = sp.Matrix(self.z_sym)
        return sp.Rational(1, 2) * z.T @ self.M_symbolic @ z

    @property
    def symbolic_gradient(self) -> sp.Matrix:
        """Symbolic gradient expression."""
        z = sp.Matrix(self.z_sym)
        return self.M_symbolic @ z


class QuadraticHamiltonian(HamiltonianBase):
    """
    Numerical Quadratic Hamiltonian for Phase 1A simulations and optimization.

    Holds fundamental physical parameters directly, which are optimized by the PEM loop.
    The M matrix is constructed dynamically from these parameters.

    State vector: z = [q_agent, p_agent, q_market, p_market]
    M matrix: M = diag([k_agent, 1/m_agent, k_market, 1/m_market])
    """

    k_agent: jax.Array
    m_agent: jax.Array
    k_market: jax.Array
    m_market: jax.Array

    def __init__(self, k_agent, m_agent, k_market, m_market):
        """
        Initialize with fundamental physical parameters.

        Args:
            k_agent: Agent stiffness parameter (must be positive)
            m_agent: Agent mass parameter (must be positive)
            k_market: Market stiffness parameter (must be positive)
            m_market: Market mass parameter (must be positive)
        """
        self.k_agent = jnp.asarray(k_agent, dtype=jnp.float32)
        self.m_agent = jnp.asarray(m_agent, dtype=jnp.float32)
        self.k_market = jnp.asarray(k_market, dtype=jnp.float32)
        self.m_market = jnp.asarray(m_market, dtype=jnp.float32)

    def _build_M_matrix(self) -> jnp.ndarray:
        """
        Dynamically builds the M matrix from current parameters.

        Returns:
            M = diag([k_agent, 1/m_agent, k_market, 1/m_market])
        """
        # Add a small epsilon for numerical stability to avoid division by zero
        m_agent_stable = self.m_agent + 1e-8
        m_market_stable = self.m_market + 1e-8
        return jnp.diag(
            jnp.array(
                [
                    self.k_agent,
                    1.0 / m_agent_stable,
                    self.k_market,
                    1.0 / m_market_stable,
                ]
            )
        )

    def value(self, z: jnp.ndarray) -> jnp.ndarray:
        """
        Compute H(z) = 0.5 * z^T * M * z.

        Args:
            z: State [q_agent, p_agent, q_market, p_market]

        Returns:
            Scalar JAX array representing the Hamiltonian value
        """
        M = self._build_M_matrix()
        return 0.5 * z.T @ M @ z

    def gradient(self, z: jnp.ndarray) -> jnp.ndarray:
        """
        Analytically compute ∇H(z) = M * z for efficiency.

        Args:
            z: State [q_agent, p_agent, q_market, p_market]
        """
        M = self._build_M_matrix()
        return M @ z

    def hessian(self, z: jnp.ndarray) -> jnp.ndarray:
        """
        Analytically compute ∇²H(z) = M.

        Args:
            z: State (unused for quadratic)
        """
        del z  # unused for quadratic
        return self._build_M_matrix()

    def is_convex(self, z: jnp.ndarray) -> bool:
        """
        Check if M is positive definite (all parameters positive).

        Args:
            z: State (unused)
        """
        del z  # unused
        # For our diagonal M matrix, positive definiteness is equivalent to all parameters being positive
        return bool(
            jnp.all(
                jnp.array([self.k_agent, self.m_agent, self.k_market, self.m_market])
                > 0
            )
        )


class PolynomialHamiltonian(HamiltonianBase):
    """Polynomial Hamiltonian for nonlinear systems (Phase 1B ground truth)."""

    # Placeholder for future Phase 1B parameters
    # Will be implemented when Phase 1B requirements are defined

    def value(self, z: jnp.ndarray) -> jnp.ndarray:
        """Compute polynomial Hamiltonian value."""
        del z  # Mark as intentionally unused
        raise NotImplementedError

    def gradient(self, z: jnp.ndarray) -> jnp.ndarray:
        """Compute gradient via automatic differentiation."""
        del z  # Mark as intentionally unused
        raise NotImplementedError

"""
Base Port-Hamiltonian System class providing the fundamental PHS structure
shared across all phases of the research.
"""

import jax.numpy as jnp
import equinox as eqx
from abc import abstractmethod
from typing import Tuple, Dict, Any, Optional


class PHSParameters(eqx.Module):
    """Container for PHS system parameters."""

    hamiltonian_params: Dict[str, Any] = eqx.field(static=True)
    dissipation_params: Dict[str, Any] = eqx.field(static=True)
    coupling_params: Dict[str, Any] = eqx.field(static=True)


class PHSBase(eqx.Module):
    """
    Base class for Port-Hamiltonian Systems implementing the fundamental equation:

    dz/dt = (J - R(z)) * ∇H(z) + g(z) * u(t) + σ(z) * w(t)

    This class provides the core structure reused across:
    - Phase 1A: Linear coupled agent-market systems
    - Phase 1B: Nonlinear synthetic agent systems
    - Phase 2: Real ARKK data discovery
    """

    state_dim: int = eqx.field(static=True)
    input_dim: int = eqx.field(static=True)
    n_dofs: int = eqx.field(static=True)
    J: jnp.n<PERSON><PERSON>

    def __init__(self, state_dim: int, input_dim: int = 0):
        """
        Initialize PHS system with dimensions and canonical structure matrices.

        Args:
            state_dim: Dimension of state vector z (must be even for canonical PHS)
            input_dim: Dimension of input vector u (0 for autonomous systems)
        """
        if state_dim % 2 != 0:
            raise ValueError(
                "State dimension must be even for canonical PHS (q and p variables)"
            )

        self.state_dim = state_dim
        self.input_dim = input_dim
        self.n_dofs = state_dim // 2  # Number of degrees of freedom

        # Build canonical symplectic structure matrix J
        self.J = self._build_canonical_J()

    def _build_canonical_J(self) -> jnp.ndarray:
        """
        Build canonical symplectic matrix J = [[0, I], [-I, 0]].

        For state z = [q1, q2, ..., p1, p2, ...] with n DOFs:
        J is (2n x 2n) with structure preserving canonical Poisson brackets.

        Returns:
            Canonical symplectic matrix J
        """
        n = self.n_dofs
        I = jnp.eye(n)
        O = jnp.zeros((n, n))

        # J = [[0, I], [-I, 0]]
        J = jnp.block([[O, I], [-I, O]])
        return J

    def dynamics(self, t, z, args):
        """
        Compute PHS dynamics: dz/dt = (J - R) * ∇H + g * u + σ * w

        Diffrax-compatible signature with dual-channel design.

        Args:
            t: Time
            z: State vector [q, p]
            args: Dictionary with:
                - 'u': external inputs (market signals, future phases)
                - 'w': process noise (stochastic excitation, Phase 1A)  
                - 'params': PHSParameters

        Returns:
            Time derivative dz/dt
        """
        params = args['params']
        u = args.get('u', None)  # External market signals
        w = args.get('w', None)  # Process noise
        
        # Core PHS dynamics: (J - R) * ∇H
        R = self.dissipation_matrix(z, params)
        grad_H = self.hamiltonian_gradient(z, params)
        dynamics = (self.J - R) @ grad_H
        
        # Add external inputs if present
        if u is not None:
            g = self.input_matrix(z, params)
            if g is not None:
                dynamics += g @ u
                
        # Add process noise if present
        if w is not None:
            sigma = self.noise_matrix(z, params)
            if sigma is not None:
                dynamics += sigma @ w
                
        return dynamics

    def energy(self, z: jnp.ndarray, params: PHSParameters) -> float:
        """Compute total system energy (Hamiltonian)."""
        raise NotImplementedError

    def power_balance(
        self, z: jnp.ndarray, u: jnp.ndarray, params: PHSParameters
    ) -> Dict[str, float]:
        """
        Compute power flow components for energy analysis.

        Returns:
            Dictionary with power dissipated, supplied, and stored
        """
        raise NotImplementedError

    @abstractmethod
    def get_default_parameters(self) -> PHSParameters:
        """Return default parameters for this PHS system."""
        pass

    # Abstract methods for PHS components
    @abstractmethod
    def hamiltonian_matrix(self, params: PHSParameters) -> jnp.ndarray:
        """
        Return Hamiltonian matrix M for quadratic form H = 0.5 * z^T * M * z.

        Args:
            params: System parameters

        Returns:
            M matrix (state_dim x state_dim)
        """
        pass

    @abstractmethod
    def hamiltonian_gradient(self, z: jnp.ndarray, params: PHSParameters) -> jnp.ndarray:
        """
        Return Hamiltonian gradient ∇H(z).

        Args:
            z: State vector
            params: System parameters

        Returns:
            ∇H vector (state_dim,)
        """
        pass

    @abstractmethod
    def dissipation_matrix(self, z: jnp.ndarray, params: PHSParameters) -> jnp.ndarray:
        """
        Return dissipation matrix R(z) >= 0.

        Args:
            z: State vector
            params: System parameters

        Returns:
            R matrix (state_dim x state_dim, positive semi-definite)
        """
        pass

    def input_matrix(
        self, z: jnp.ndarray, params: PHSParameters
    ) -> Optional[jnp.ndarray]:
        """
        Return input matrix g(z) for external forces.

        Args:
            z: State vector
            params: System parameters

        Returns:
            g matrix (state_dim x input_dim) or None for autonomous systems
        """
        if self.input_dim == 0:
            return None
        # Default: no input coupling
        return jnp.zeros((self.state_dim, self.input_dim))

    def noise_matrix(
        self, z: jnp.ndarray, params: PHSParameters
    ) -> Optional[jnp.ndarray]:
        """
        Return noise coupling matrix σ(z) for process noise w(t).

        Args:
            z: State vector
            params: System parameters

        Returns:
            σ matrix (state_dim x noise_dim) or None for deterministic systems
        """
        # Default: no noise coupling
        return None

    def get_market_noise_matrix(
        self, sigma_market: float = 1.0
    ) -> jnp.ndarray:
        """
        Get Phase 1A market noise coupling matrix.
        
        For Phase 1A: external order flow enters only through market price (p_market).
        Economic interpretation: External order flow → Market price → Agent response
        
        Args:
            sigma_market: Market noise intensity (order flow volatility)
            
        Returns:
            Noise coupling matrix E where noise acts only on p_market
        """
        # For 4-state system [q_agent, p_agent, q_market, p_market]
        # Noise enters only through p_market (index 3)
        if self.state_dim != 4:
            raise ValueError(
                f"Market noise matrix designed for 4-state Phase 1A system, "
                f"got {self.state_dim} states"
            )
            
        E = jnp.zeros((self.state_dim, 1))  # Single noise source
        E = E.at[3, 0].set(sigma_market)   # Noise on p_market only
        
        return E

    # State-space conversion methods
    def get_state_space_matrices(
        self, params: PHSParameters
    ) -> Tuple[jnp.ndarray, Optional[jnp.ndarray]]:
        """
        Convert PHS to state-space form: dz/dt = A*z + B*u

        For linear PHS: A = (J - R) @ M
        For input coupling: B = g (if inputs present)

        Args:
            params: System parameters

        Returns:
            (A, B) matrices for state-space representation
        """
        M = self.hamiltonian_matrix(params)
        # For linear systems, R is constant (evaluate at z=0)
        z_ref = jnp.zeros(self.state_dim)
        R = self.dissipation_matrix(z_ref, params)

        # A = (J - R) @ M
        A = (self.J - R) @ M

        # B matrix (if inputs present)
        if self.input_dim > 0:
            g = self.input_matrix(z_ref, params)
            B = g
        else:
            B = None

        return A, B

    def get_observation_matrix(
        self, observed_states: Optional[list] = None
    ) -> jnp.ndarray:
        """
        Build observation matrix C for partial observations y = C*z.

        Args:
            observed_states: List of state indices to observe (None = full state)

        Returns:
            C matrix (n_obs x state_dim)
        """
        if observed_states is None:
            # Full state observation
            return jnp.eye(self.state_dim)
        else:
            # Partial state observation
            n_obs = len(observed_states)
            C = jnp.zeros((n_obs, self.state_dim))
            for i, state_idx in enumerate(observed_states):
                C = C.at[i, state_idx].set(1.0)
            return C


"""
State variable definitions and utilities for PHS systems.

Provides consistent interpretation of z = [q, p] across all phases:
- q: Holdings/positions (generalized coordinates)
- p: Internal reservation prices (generalized momenta/economic momentum)
"""

# DECISION: will only be used starting from phase 1B
from typing import Optional, Tuple

import jax
import jax.numpy as jnp
import equinox as eqx


class StateVariables(eqx.Module):
    """
    Container for PHS state variables with economic interpretation.

    The state vector z = [q, p] where:
    - q: Holdings/asset quantities (generalized coordinates)
    - p: Internal reservation prices (generalized momenta/economic momentum)
    """

    q: jnp.ndarray  # Holdings
    p: jnp.ndarray  # Internal prices

    def __init__(self, q: jnp.ndarray, p: jnp.ndarray):
        """Initialize state variables."""
        self.q = q
        self.p = p

    @property
    def z(self) -> jnp.ndarray:
        """Return combined state vector [q, p]."""
        return jnp.concatenate([self.q, self.p])

    @classmethod
    def from_state_vector(cls, z: jnp.ndarray) -> "StateVariables":
        """Create StateVariables from combined state vector z."""
        n_dofs = len(z) // 2
        q = z[:n_dofs]
        p = z[n_dofs:]
        return cls(q=q, p=p)


class StateTrajectory(eqx.Module):
    """Container for time series of state variables."""
    
    times: jnp.ndarray
    states: jnp.ndarray  # Shape: (n_timesteps, state_dim)

    def __init__(self, times: jnp.ndarray, states: jnp.ndarray):
        """Initialize state trajectory."""
        self.times = times
        self.states = states

    def get_state_at_time(self, time_idx: int) -> StateVariables:
        """Get StateVariables at specific time index."""
        z = self.states[time_idx]
        return StateVariables.from_state_vector(z)

    def split_trajectory(self) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """Split trajectory into q and p components."""
        n_dofs = self.states.shape[1] // 2
        q_traj = self.states[:, :n_dofs]
        p_traj = self.states[:, n_dofs:]
        return q_traj, p_traj

    def compute_derivatives(self, method: str = "finite_difference") -> jnp.ndarray:
        """Compute time derivatives of state trajectory."""
        if method == "finite_difference":
            dt = jnp.diff(self.times)
            dz_dt = jnp.diff(self.states, axis=0) / dt[:, None]
            return dz_dt
        else:
            raise NotImplementedError(f"Method {method} not implemented")


def create_initial_state(
    n_assets: int,
    q_init: Optional[jnp.ndarray] = None,
    p_init: Optional[jnp.ndarray] = None,
    random_key: Optional[jax.random.PRNGKey] = None,
) -> StateVariables:
    """Create initial state for PHS system."""
    if q_init is None:
        if random_key is not None:
            key1, key2 = jax.random.split(random_key)
            q_init = jax.random.normal(key1, (n_assets,)) * 0.1
        else:
            q_init = jnp.zeros(n_assets)
    
    if p_init is None:
        if random_key is not None:
            key1, key2 = jax.random.split(random_key, 2) if random_key is not None else (None, None)
            p_init = jax.random.normal(key2, (n_assets,)) * 0.1 if key2 is not None else jnp.zeros(n_assets)
        else:
            p_init = jnp.zeros(n_assets)
    
    return StateVariables(q=q_init, p=p_init)


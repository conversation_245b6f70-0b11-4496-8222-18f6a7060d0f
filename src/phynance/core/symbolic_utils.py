from typing import <PERSON><PERSON>

import sympy as sp


def construct_phs_state_matrix(
    J_mat: sp.Matrix, R_mat: sp.Matrix, M_mat: sp.Matrix
) -> sp.Matrix:
    """
    Construct the PHS state matrix A = (J - R) * M.

    Args:
        J_mat: Canonical symplectic matrix J
        R_mat: Dissipation matrix R
        M_mat: Hamiltonian matrix M (for quadratic H = 0.5*z^T*M*z)

    Returns:
        PHS state matrix A = (J - R) * M
    """
    return (J_mat - R_mat) * M_mat


def construct_observability_matrix(A: sp.Matrix, C: sp.Matrix, n: int) -> sp.Matrix:
    """
    Construct the observability matrix for a linear system.

    Args:
        A: State transition matrix.
        C: Output matrix.
        n: Number of states.

    Returns:
        Observability matrix O.
    """
    obs_rows = [C]
    for i in range(1, n):
        obs_rows.append(C * A**i)
    return sp.Matrix.vstack(*obs_rows)


def check_observability_rank(obs_mat: sp.Matrix) -> <PERSON><PERSON>[bool, int]:
    """
    Check the rank of the observability matrix.

    Args:
        O: Observability matrix.

    Returns:
        Tuple containing:
        - bool: True if the system is observable, False otherwise.
        - int: Rank of the observability matrix.
    """
    rank = obs_mat.rank()
    n_states = obs_mat.shape[1]  # Number of columns = number of states
    is_observable = rank == n_states
    return is_observable, rank


def construct_transfer_function(
    A_mat: sp.Matrix, C_mat: sp.Matrix, s: sp.Symbol
) -> sp.Matrix:
    """
    Construct the transfer function matrix for a linear system.

    Given by G(s)=C(sI-A)^(-1)

    Args:
        A: State transition matrix.
        C: Output matrix.
        s: Laplace variable.

    Returns:
        Transfer function matrix H(s).
    """
    eye = sp.eye(A_mat.shape[0])
    return C_mat * (s * eye - A_mat).inv()


def check_controllability_rank(A_mat: sp.Matrix, B_mat: sp.Matrix) -> Tuple[bool, int]:
    """
    Check controllability using the Kalman rank condition.

    Constructs controllability matrix [B, AB, A²B, ..., A^(n-1)B] and checks rank.

    Args:
        A_mat: State transition matrix (n×n)
        B_mat: Input matrix (n×m)

    Returns:
        (is_controllable, rank) - True if rank equals n, actual rank value
    """
    n = A_mat.shape[0]

    # Build controllability matrix [B, AB, A²B, ..., A^(n-1)B]
    controllability_matrix = B_mat
    A_power = sp.eye(n)

    for _ in range(1, n):
        A_power = A_power * A_mat
        controllability_matrix = controllability_matrix.row_join(A_power * B_mat)

    rank = controllability_matrix.rank()
    is_controllable = rank == n

    return is_controllable, rank


def analyze_structural_identifiability(
    G_s: sp.Matrix, parameters: list, s: sp.Symbol
) -> dict:
    """
    Analyze structural identifiability via transfer function coefficient analysis.

    Examines whether the mapping from parameters to transfer function coefficients
    is one-to-one (globally structurally identifiable).

    Args:
        G_s: Transfer function matrix G(s, θ)
        parameters: List of parameter symbols θ
        s: Laplace variable symbol

    Returns:
        Dictionary with:
        - 'identifiable': bool indicating if structurally identifiable
        - 'coefficients': extracted polynomial coefficients
        - 'parameter_mapping': coefficient expressions in terms of parameters
        - 'constraints': any identifiability constraints found
    """
    results = {
        "identifiable": False,
        "coefficients": {},
        "parameter_mapping": {},
        "constraints": [],
    }

    try:
        # Extract coefficients from each transfer function element
        n_outputs, n_inputs = G_s.shape

        for i in range(n_outputs):
            for j in range(n_inputs):
                tf_element = G_s[i, j]

                try:
                    # For rational functions, extract numerator and denominator coefficients
                    numer, denom = sp.fraction(tf_element)

                    # Extract coefficients from numerator
                    if sp.degree(numer, s) >= 0:
                        numer_poly = sp.Poly(numer, s)
                        numer_coeffs = numer_poly.all_coeffs()
                        for k, coeff in enumerate(numer_coeffs):
                            power = len(numer_coeffs) - 1 - k
                            coeff_key = f"G_{i}{j}_num_s{power}"
                            results["coefficients"][coeff_key] = coeff

                    # Extract coefficients from denominator
                    if sp.degree(denom, s) >= 0:
                        denom_poly = sp.Poly(denom, s)
                        denom_coeffs = denom_poly.all_coeffs()
                        for k, coeff in enumerate(denom_coeffs):
                            power = len(denom_coeffs) - 1 - k
                            coeff_key = f"G_{i}{j}_den_s{power}"
                            results["coefficients"][coeff_key] = coeff

                except Exception as e:
                    results["constraints"].append(
                        f"Error extracting coefficients from G_{i}{j}: {str(e)}"
                    )

                # Check parameter mapping for all collected coefficients
                for coeff_key, coeff in results["coefficients"].items():
                    if coeff_key not in results["parameter_mapping"]:
                        coeff_params = set()
                        for param in parameters:
                            if coeff.has(param):
                                coeff_params.add(param)
                        results["parameter_mapping"][coeff_key] = coeff_params

        # Simple identifiability check:
        # If we have at least as many independent coefficients as parameters
        n_params = len(parameters)
        n_coeffs = len(results["coefficients"])

        if n_coeffs >= n_params:
            # Check if coefficient Jacobian with respect to parameters has full rank
            coeff_list = list(results["coefficients"].values())
            jacobian = sp.Matrix([coeff_list]).jacobian(parameters)

            if jacobian.shape[1] > 0:  # Avoid empty jacobian
                jac_rank = jacobian.rank()
                results["identifiable"] = jac_rank == n_params

                if jac_rank < n_params:
                    results["constraints"].append(
                        f"Coefficient Jacobian rank {jac_rank} < "
                        f"{n_params} parameters. "
                        "Some parameters may not be identifiable."
                    )
        else:
            results["constraints"].append(
                f"Only {n_coeffs} coefficients available for {n_params} parameters. "
                "System is likely unidentifiable."
            )

    except Exception as e:
        results["constraints"].append(f"Error in identifiability analysis: {str(e)}")

    return results


def extract_parameter_constraints(
    H_matrix: sp.Matrix, R_matrix: sp.Matrix, parameters: list
) -> list:
    """
    Extract physical parameter constraints for PHS systems.

    PHS systems require:
    - H positive definite (or positive semidefinite)
    - R positive semidefinite
    - Additional coupling constraints

    Args:
        H_matrix: Symbolic Hamiltonian matrix M
        R_matrix: Symbolic dissipation matrix R
        parameters: List of parameter symbols

    Returns:
        List of constraint expressions that must be satisfied
    """
    constraints = []

    # Hamiltonian constraints - positive definiteness
    # For diagonal M matrix: all diagonal elements > 0
    for i in range(H_matrix.shape[0]):
        diag_elem = H_matrix[i, i]
        if diag_elem != 0:  # Skip zero diagonal elements
            constraints.append(f"{diag_elem} > 0")

    # Dissipation constraints - positive semidefiniteness
    # For our specific R structure, check individual and coupling constraints
    for i in range(R_matrix.shape[0]):
        for j in range(i, R_matrix.shape[1]):  # Upper triangular
            elem = R_matrix[i, j]
            if elem != 0:
                if i == j:  # Diagonal element
                    constraints.append(f"{elem} >= 0")
                else:  # Off-diagonal coupling constraint
                    # For 2x2 block positive semidefinite: det >= 0
                    if R_matrix[j, i] == elem:  # Symmetric coupling
                        constraints.append(f"coupling constraint: {elem}")

    # Additional physical bounds (prevent unbounded parameters)
    for param in parameters:
        param_str = str(param)
        if "m_" in param_str:  # Mass parameters
            constraints.append(f"{param} > 0")
        elif "k_" in param_str:  # Stiffness parameters
            constraints.append(f"{param} > 0")
        elif "r_" in param_str:  # Resistance parameters
            constraints.append(f"{param} >= 0")

    return constraints


def construct_noise_coupling_matrix(
    state_names: list, noise_targets: list | None = None
) -> sp.Matrix:
    """
    Construct symbolic noise coupling matrix E for stochastic PHS.

    For Phase 1A: external order flow enters only through market price (p_market).

    Args:
        state_names: List of state variable names
            ['q_agent', 'p_agent', 'q_market', 'p_market']
        noise_targets: List of states that receive noise (default: ['p_market'])

    Returns:
        Symbolic noise coupling matrix E (n_states × n_noise_sources)
    """
    if noise_targets is None:
        noise_targets = ["p_market"]

    n_states = len(state_names)
    n_noise = len(noise_targets)

    # Create symbolic noise intensities
    noise_symbols = [sp.Symbol(f"sigma_{target}") for target in noise_targets]

    # Build noise coupling matrix
    E = sp.zeros(n_states, n_noise)

    for noise_idx, target in enumerate(noise_targets):
        if target in state_names:
            state_idx = state_names.index(target)
            E[state_idx, noise_idx] = noise_symbols[noise_idx]

    return E

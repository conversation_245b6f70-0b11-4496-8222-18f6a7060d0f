"""
Identifiability Analyzer for Phase 1A Linear De-coupling Gateway.

Implements the formal structural identifiability analysis that serves as the critical
theoretical prerequisite before numerical PEM identification can be attempted.

This is the "gateway" that must be passed to validate the fundamental premise:
agent parameters can be recovered even in challenging coupled scenarios.
"""

from typing import Any, Dict, List, Optional, Tuple

import sympy as sp
from sympy import Matrix, symbols

from ..core.symbolic_utils import (
    analyze_structural_identifiability,
    check_controllability_rank,
    check_observability_rank,
    construct_noise_coupling_matrix,
    construct_observability_matrix,
    construct_transfer_function,
    extract_parameter_constraints,
)


class IdentifiabilityAnalyzer:
    """
    Core gateway validation system for Phase 1A theoretical analysis.
    
    Implements the two critical mathematical conditions required before
    numerical identification can proceed:
    
    1. Observability: Can you see the internal states from measurements?
    2. Structural Identifiability: Can you uniquely determine the parameters?
    
    Success here validates that the fundamental inverse problem is solvable.
    """

    def __init__(self, 
                 state_names: List[str],
                 parameter_names: List[str],
                 observed_states: Optional[List[str]] = None):
        """
        Initialize analyzer for Phase 1A coupled system.
        
        Args:
            state_names: List of state variables ['q_agent', 'p_agent', 'q_market', 'p_market']
            parameter_names: List of parameters to identify (focus on agent parameters)
            observed_states: Which states can be measured (default: positions only)
        """
        self.state_names = state_names
        self.parameter_names = parameter_names
        
        # Default: observe q_agent and p_market - correct sensor-actuator relationship
        if observed_states is None:
            self.observed_states = ['q_agent', 'p_market']
        else:
            self.observed_states = observed_states
            
        # Create symbolic variables
        self.states_sym = [symbols(name) for name in state_names]
        self.params_sym = [symbols(name) for name in parameter_names]
        self.s = symbols('s')  # Laplace variable
        
        # Build observation matrix C
        self.C_matrix = self._build_observation_matrix()
        
        # Gateway validation results
        self._gateway_results: Optional[Dict[str, Any]] = None

    def _build_observation_matrix(self) -> Matrix:
        """
        Build observation matrix C for partial state observation.
        
        Returns:
            C matrix mapping full state to observed outputs
        """
        n_states = len(self.state_names)
        n_outputs = len(self.observed_states)
        
        C = sp.zeros(n_outputs, n_states)
        
        for i, obs_state in enumerate(self.observed_states):
            if obs_state in self.state_names:
                j = self.state_names.index(obs_state)
                C[i, j] = 1
        
        return C

    def gateway_analysis(self, 
                        A_symbolic: Matrix, 
                        M_symbolic: Matrix,
                        R_symbolic: Matrix,
                        noise_targets: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Complete Phase 1A theoretical gateway validation.
        
        This is the critical go/no-go analysis that must pass before numerical
        identification can be attempted.
        
        Args:
            A_symbolic: Symbolic state matrix A(θ) = (J-R)M
            M_symbolic: Symbolic Hamiltonian matrix M(θ)
            R_symbolic: Symbolic dissipation matrix R(θ)
            noise_targets: States receiving process noise (default: ['p_market'])
            
        Returns:
            Complete gateway analysis results with pass/fail decision
        """
        results = {
            'gateway_passed': False,
            'observability': {},
            'controllability': {},
            'identifiability': {},
            'constraints': {},
            'recommendations': [],
            'summary': {}
        }
        
        try:
            # Step 1: Observability Analysis
            print("Phase 1A Gateway: Analyzing observability...")
            results['observability'] = self.observability_analysis(A_symbolic)
            
            # Step 2: Controllability Analysis  
            print("Phase 1A Gateway: Analyzing controllability...")
            results['controllability'] = self.controllability_analysis(
                A_symbolic, noise_targets
            )
            
            # Step 3: Structural Identifiability Analysis
            print("Phase 1A Gateway: Analyzing structural identifiability...")
            results['identifiability'] = self.identifiability_analysis(A_symbolic)
            
            # Step 4: Parameter Constraints
            print("Phase 1A Gateway: Extracting parameter constraints...")
            results['constraints'] = self.constraints_analysis(M_symbolic, R_symbolic)
            
            # Step 5: Gateway Decision
            results = self._make_gateway_decision(results)
            
            # Cache results
            self._gateway_results = results
            
        except Exception as e:
            results['error'] = str(e)
            results['recommendations'].append(
                f"Gateway analysis failed with error: {str(e)}"
            )
            
        return results

    def _format_matrix_latex(self, matrix: sp.Matrix, name: str) -> str:
        """Format a SymPy matrix in LaTeX notation for reports."""
        # Convert matrix to LaTeX string
        latex_str = sp.latex(matrix)
        return f"$${name} = {latex_str}$$"

    def _format_matrix_text(self, matrix: sp.Matrix, name: str, max_size: int = 6) -> str:
        """Format a SymPy matrix in readable text format for reports."""
        rows, cols = matrix.shape
        if rows > max_size or cols > max_size:
            return f"{name} ({rows}×{cols} matrix - too large to display)"
        
        # Format matrix elements
        result = f"{name} = \n"
        result += "⎡" if rows > 1 else "["
        
        for i in range(rows):
            if i > 0:
                result += " ⎢" if i < rows - 1 else " ⎣"
            for j in range(cols):
                elem = str(matrix[i, j])
                # Truncate very long expressions
                if len(elem) > 12:
                    elem = elem[:9] + "..."
                result += f"{elem:>12}"
            result += " ⎤\n" if i == 0 and rows > 1 else " ⎥\n" if i < rows - 1 else " ⎦\n" if rows > 1 else "]\n"
        
        return result

    def observability_analysis(self, A_symbolic: Matrix) -> Dict[str, Any]:
        """
        Kalman rank condition analysis on symbolic system matrices.
        
        Tests whether all internal states can be inferred from the chosen
        measurements (typically only position observations).
        
        Args:
            A_symbolic: Symbolic state matrix A(θ)
            
        Returns:
            Observability analysis results
        """
        n_states = len(self.state_names)
        
        # Construct observability matrix O = [C; CA; CA²; ...; CA^(n-1)]
        O = construct_observability_matrix(A_symbolic, self.C_matrix, n_states)
        
        # Check rank symbolically
        is_observable, rank = check_observability_rank(O)
        
        results = {
            'observability_matrix_shape': O.shape,
            'rank': rank,
            'required_rank': n_states,
            'is_observable': is_observable,
            'observed_states': self.observed_states,
            'unobservable_states': [],
            'matrices': {
                'A_matrix': A_symbolic,
                'C_matrix': self.C_matrix,
                'observability_matrix': O,
                'A_latex': self._format_matrix_latex(A_symbolic, 'A'),
                'C_latex': self._format_matrix_latex(self.C_matrix, 'C'),
                'O_latex': self._format_matrix_latex(O, '\\mathcal{O}'),
                'A_text': self._format_matrix_text(A_symbolic, 'A'),
                'C_text': self._format_matrix_text(self.C_matrix, 'C'),
                'O_text': self._format_matrix_text(O, 'O')
            }
        }
        
        if not is_observable:
            # Identify which states might be unobservable
            unobs_candidates = []
            for state in self.state_names:
                if state not in self.observed_states:
                    unobs_candidates.append(state)
            results['unobservable_states'] = unobs_candidates
            
        return results

    def controllability_analysis(self, 
                                A_symbolic: Matrix,
                                noise_targets: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Controllability analysis for stochastic excitation.
        
        Tests whether external noise can excite all system modes, ensuring
        sufficient persistent excitation for parameter identification.
        
        Args:
            A_symbolic: Symbolic state matrix A(θ)
            noise_targets: States receiving noise (default: ['p_market'])
            
        Returns:
            Controllability analysis results
        """
        # Build noise coupling matrix E
        E = construct_noise_coupling_matrix(self.state_names, noise_targets)
        
        # Check controllability rank
        is_controllable, rank = check_controllability_rank(A_symbolic, E)
        
        results = {
            'noise_coupling_matrix_shape': E.shape,
            'rank': rank,
            'required_rank': len(self.state_names),
            'is_controllable': is_controllable,
            'noise_targets': noise_targets or ['p_market'],
            'excitation_analysis': self._analyze_excitation_patterns(E)
        }
        
        return results

    def identifiability_analysis(self, A_symbolic: Matrix) -> Dict[str, Any]:
        """
        Structural identifiability analysis via transfer function coefficient matching.
        
        Tests whether the mapping from parameters to transfer function coefficients
        is one-to-one, ensuring unique parameter recovery is theoretically possible.
        
        Args:
            A_symbolic: Symbolic state matrix A(θ)
            
        Returns:
            Structural identifiability analysis results
        """
        # Construct transfer function G(s,θ) = C(sI - A(θ))^(-1)
        G_s = construct_transfer_function(A_symbolic, self.C_matrix, self.s)
        
        # Analyze structural identifiability
        identifiability_results = analyze_structural_identifiability(
            G_s, self.params_sym, self.s
        )
        
        # Focus on agent parameters specifically
        agent_params = [p for p in self.params_sym if 'agent' in str(p)]
        
        results = {
            'transfer_function_shape': G_s.shape,
            'all_parameters': len(self.params_sym),
            'agent_parameters': len(agent_params),
            'coefficients_found': len(identifiability_results['coefficients']),
            'globally_identifiable': identifiability_results['identifiable'],
            'coefficient_analysis': identifiability_results,
            'agent_parameter_focus': self._analyze_agent_identifiability(
                identifiability_results, agent_params
            ),
            'matrices': {
                'transfer_function': G_s,
                'G_latex': self._format_matrix_latex(G_s, 'G(s)'),
                'G_text': self._format_matrix_text(G_s, 'G(s)', max_size=4)
            }
        }
        
        return results

    def constraints_analysis(self, 
                           M_symbolic: Matrix,
                           R_symbolic: Matrix) -> Dict[str, Any]:
        """
        Extract and analyze physical parameter constraints.
        
        PHS systems have inherent physical constraints that must be satisfied
        for the system to be realizable and stable.
        
        Args:
            M_symbolic: Symbolic Hamiltonian matrix M(θ)
            R_symbolic: Symbolic dissipation matrix R(θ)
            
        Returns:
            Parameter constraint analysis
        """
        constraints = extract_parameter_constraints(
            M_symbolic, R_symbolic, self.params_sym
        )
        
        # Categorize constraints
        positivity_constraints = [c for c in constraints if '> 0' in c]
        semidefinite_constraints = [c for c in constraints if '>= 0' in c]
        coupling_constraints = [c for c in constraints if 'coupling' in c]
        
        results = {
            'total_constraints': len(constraints),
            'positivity_constraints': positivity_constraints,
            'semidefinite_constraints': semidefinite_constraints,
            'coupling_constraints': coupling_constraints,
            'all_constraints': constraints,
            'constraint_verification': self._verify_constraint_consistency(constraints)
        }
        
        return results

    def _analyze_excitation_patterns(self, E: Matrix) -> Dict[str, Any]:
        """Analyze which states receive direct noise excitation."""
        excitation_pattern = {}
        
        for i, state in enumerate(self.state_names):
            # Check if this state receives direct noise
            has_direct_noise = any(E[i, j] != 0 for j in range(E.shape[1]))
            excitation_pattern[state] = has_direct_noise
            
        return {
            'direct_excitation_pattern': excitation_pattern,
            'directly_excited_states': [s for s, excited in excitation_pattern.items() if excited],
            'unexcited_states': [s for s, excited in excitation_pattern.items() if not excited]
        }

    def _analyze_agent_identifiability(self, 
                                     identifiability_results: Dict[str, Any],
                                     agent_params: List) -> Dict[str, Any]:
        """Focus identifiability analysis on agent parameters specifically."""
        agent_analysis = {
            'agent_parameter_count': len(agent_params),
            'agent_parameters': [str(p) for p in agent_params],
            'coefficients_containing_agent_params': {}
        }
        
        # Check which coefficients contain agent parameters
        for coeff_name, param_set in identifiability_results.get('parameter_mapping', {}).items():
            agent_params_in_coeff = [p for p in param_set if 'agent' in str(p)]
            if agent_params_in_coeff:
                agent_analysis['coefficients_containing_agent_params'][coeff_name] = agent_params_in_coeff
        
        return agent_analysis

    def _verify_constraint_consistency(self, constraints: List[str]) -> Dict[str, Any]:
        """Verify that parameter constraints are mathematically consistent."""
        verification = {
            'potentially_conflicting': [],
            'redundant_constraints': [],
            'well_posed': True
        }
        
        # Basic consistency checks
        # This is a simplified check - more sophisticated analysis could be added
        param_bounds = {}
        for constraint in constraints:
            for param in self.params_sym:
                if str(param) in constraint:
                    if param not in param_bounds:
                        param_bounds[param] = []
                    param_bounds[param].append(constraint)
        
        verification['parameter_bound_analysis'] = {
            str(k): v for k, v in param_bounds.items()
        }
        
        return verification

    def _make_gateway_decision(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make the critical go/no-go gateway decision.
        
        Determines whether Phase 1A theoretical validation has passed
        and numerical PEM identification can proceed.
        
        Key insight: Full controllability is NOT required for identification.
        Observability + Structural Identifiability are the critical conditions.
        """
        # Extract key criteria
        is_observable = results['observability'].get('is_observable', False)
        is_controllable = results['controllability'].get('is_controllable', False)  
        is_identifiable = results['identifiability'].get('globally_identifiable', False)
        
        # Gateway decision logic: Observability + Identifiability are critical
        # Controllability is helpful but not essential for identification
        gateway_passed = is_observable and is_identifiable
        gateway_status = "PASSED"
        
        if not is_controllable:
            gateway_status = "PASSED with WARNING"
        
        # Generate recommendations
        recommendations = []
        if not is_observable:
            recommendations.append(
                "CRITICAL: System is not observable. Consider measuring additional states "
                f"beyond {results['observability']['observed_states']}. "
                "Suggested: Add momentum measurements (p_agent, p_market) if possible."
            )
            
        if not is_controllable and gateway_passed:
            recommendations.append(
                "NOTE: Limited controllability detected. Only 2/4 states directly excited by noise. "
                "This is acceptable for identification if persistent excitation is sufficient. "
                "The successful identifiability test confirms the noise provides adequate information."
            )
        elif not is_controllable:
            recommendations.append(
                "WARNING: System may not be fully controllable with current noise model. "
                "Consider additional noise sources or verify noise coupling assumptions."
            )
            
        if not is_identifiable:
            recommendations.append(
                "CRITICAL: Parameters may not be structurally identifiable. "
                "Review parameter-to-coefficient mapping. Some parameters may be "
                "indistinguishable from the chosen input-output data."
            )
            
        if gateway_passed and is_controllable:
            recommendations.append(
                "✅ GATEWAY PASSED: All theoretical conditions satisfied. "
                "Proceed to numerical PEM identification with confidence."
            )
        elif gateway_passed:
            recommendations.append(
                f"✅ GATEWAY {gateway_status}: Critical conditions satisfied. "
                "Observability + Identifiability confirmed. Limited controllability is "
                "acceptable for identification purposes. Proceed to numerical PEM validation."
            )
        else:
            recommendations.append(
                "❌ GATEWAY FAILED: Critical theoretical prerequisites not met. "
                "Numerical identification may fail or produce unreliable results. "
                "Address the above issues before proceeding."
            )
        
        # Update results
        results['gateway_passed'] = gateway_passed
        results['recommendations'] = recommendations
        results['summary'] = {
            'observability_check': '✅ PASS' if is_observable else '❌ FAIL',
            'controllability_check': '✅ PASS' if is_controllable else '⚠️  WARNING',
            'identifiability_check': '✅ PASS' if is_identifiable else '❌ FAIL',
            'overall_gateway': f'✅ {gateway_status}' if gateway_passed else '❌ FAILED'
        }
        
        return results

    def generate_gateway_report(self) -> str:
        """
        Generate a comprehensive gateway validation report.
        
        Returns:
            Formatted report string for Phase 1A validation
        """
        if self._gateway_results is None:
            return "Gateway analysis not yet performed. Run gateway_analysis() first."
        
        results = self._gateway_results
        
        report = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Phase 1A Linear De-coupling Gateway                      ║
║                         Theoretical Validation Report                       ║
╠══════════════════════════════════════════════════════════════════════════════╣

🎯 OBJECTIVE: Validate that agent parameters can be recovered from coupled systems

📊 SYSTEM CONFIGURATION:
   • States: {len(self.state_names)} ({', '.join(self.state_names)})
   • Parameters: {len(self.parameter_names)} ({', '.join(self.parameter_names)})
   • Observations: {len(self.observed_states)} ({', '.join(self.observed_states)})

╔══════════════════════════════════════════════════════════════════════════════╗
║                              GATEWAY RESULTS                                ║
╚══════════════════════════════════════════════════════════════════════════════╝

{results['summary']['observability_check']:20} Observability Analysis
   Rank: {results['observability']['rank']}/{results['observability']['required_rank']}
   
{results['summary']['controllability_check']:20} Controllability Analysis  
   Rank: {results['controllability']['rank']}/{results['controllability']['required_rank']}
   Noise targets: {results['controllability']['noise_targets']}
   
{results['summary']['identifiability_check']:20} Structural Identifiability
   Coefficients: {results['identifiability']['coefficients_found']}
   Agent parameters: {results['identifiability']['agent_parameters']}

╔══════════════════════════════════════════════════════════════════════════════╗
║                            FINAL DECISION                                   ║
╚══════════════════════════════════════════════════════════════════════════════╝

{results['summary']['overall_gateway']:30}

RECOMMENDATIONS:
"""
        
        for i, rec in enumerate(results['recommendations'], 1):
            report += f"\n{i:2}. {rec}"
            
        report += f"""

╔══════════════════════════════════════════════════════════════════════════════╗
║                          NEXT STEPS                                         ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        
        if results['gateway_passed']:
            report += """
✅ PROCEED TO NUMERICAL VALIDATION:
   1. Generate synthetic data using stochastic PHS simulation  
   2. Implement PEM identification with Kalman filtering
   3. Validate parameter recovery against ground truth
   4. Verify statistical quality of identification results
"""
        else:
            report += """
❌ RESOLVE THEORETICAL ISSUES FIRST:
   1. Address observability/identifiability problems above
   2. Consider alternative measurement strategies
   3. Review system parameterization for identifiability
   4. Re-run gateway analysis after modifications
"""
        
        report += "\n" + "═" * 80 + "\n"
        
        return report

    def get_gateway_status(self) -> Tuple[bool, str]:
        """
        Get simple gateway pass/fail status.
        
        Returns:
            (passed, summary_message)
        """
        if self._gateway_results is None:
            return False, "Gateway analysis not performed"
            
        passed = self._gateway_results['gateway_passed']
        summary = self._gateway_results['summary']['overall_gateway']
        
        return passed, summary
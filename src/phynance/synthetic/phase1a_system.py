"""
Phase 1A Complete System: Linear De-coupling Gateway Implementation.

This module provides the complete Phase 1A "white-box" coupled agent-market system
for theoretical and numerical validation of the fundamental inverse problem.

Key Features:
- 4-state coupled PHS system: [q_agent, p_agent, q_market, p_market]
- Bidirectional coupling between agent and market maker
- Market noise entering through p_market (external order flow)
- Complete symbolic analysis and gateway validation
- PEM identification framework integration

This is the critical first validation step that must pass before proceeding
to Phase 1B nonlinear discovery.
"""

from typing import Dict, List, Any, Optional, Tuple
import jax
import jax.numpy as jnp
import equinox as eqx
import sympy as sp
from sympy import symbols, Matrix

from ..core.phs_base import PHSBase, PHSParameters
from ..core.hamiltonian_base import SymbolicQuadraticHamiltonian, QuadraticHamiltonian
from ..core.dissipation_base import SymbolicDissipationMatrix, CoupledDissipation
from ..core.symbolic_utils import construct_phs_state_matrix
from ..identification.identifiability_analyzer import IdentifiabilityAnalyzer


class Phase1AParameters(eqx.Module):
    """
    Parameter container for Phase 1A coupled system.
    
    Separates agent, market, and coupling parameters for clear attribution
    during identification validation.
    """
    
    # Agent parameters (target for identification)
    k_agent: float = eqx.field()
    m_agent: float = eqx.field()
    r_agent: float = eqx.field()
    
    # Market parameters (known or identified alongside)
    k_market: float = eqx.field()
    m_market: float = eqx.field()
    r_market: float = eqx.field()
    
    # Coupling parameters (bidirectional feedback)
    r_coupling: float = eqx.field()
    
    # Noise parameters
    sigma_market: float = eqx.field()

    def to_phs_parameters(self) -> PHSParameters:
        """Convert to standard PHSParameters format."""
        return PHSParameters(
            hamiltonian_params={
                'k_agent': self.k_agent,
                'm_agent': self.m_agent,
                'k_market': self.k_market,
                'm_market': self.m_market,
            },
            dissipation_params={
                'r_agent': self.r_agent,
                'r_market': self.r_market,
                'r_coupling': self.r_coupling,
            },
            coupling_params={
                'sigma_market': self.sigma_market,
            }
        )

    def get_agent_parameters_only(self) -> Dict[str, float]:
        """Extract only agent parameters for identification validation."""
        return {
            'k_agent': self.k_agent,
            'm_agent': self.m_agent,
            'r_agent': self.r_agent,
        }


class Phase1ACoupledSystem(PHSBase):
    """
    Phase 1A Linear De-coupling Gateway: Complete coupled agent-market system.
    
    Implements the white-box coupled system where both agent and market maker
    are PHS subsystems with bidirectional coupling. This creates the most
    challenging identifiability scenario for gateway validation.
    
    System formulation:
    dz/dt = (J - R(θ)) * M(θ) * z + E * dW(t)
    
    where z = [q_agent, p_agent, q_market, p_market]^T
    and external order flow noise enters only through p_market.
    """

    # Symbolic components for analysis
    symbolic_hamiltonian: SymbolicQuadraticHamiltonian = eqx.field(static=True)
    symbolic_dissipation: SymbolicDissipationMatrix = eqx.field(static=True)
    
    # Identifiability analyzer
    analyzer: IdentifiabilityAnalyzer = eqx.field(static=True)

    def __init__(self):
        """Initialize Phase 1A coupled system with 4-state configuration."""
        
        # Initialize base PHS with 4 states (no external inputs in basic setup)
        super().__init__(state_dim=4, input_dim=0)
        
        # Initialize symbolic components
        self.symbolic_hamiltonian = SymbolicQuadraticHamiltonian()
        self.symbolic_dissipation = SymbolicDissipationMatrix()
        
        # Initialize identifiability analyzer
        state_names = ['q_agent', 'p_agent', 'q_market', 'p_market']
        parameter_names = [
            'k_agent', 'm_agent', 'k_market', 'm_market',
            'r_agent', 'r_market', 'r_coupling'
        ]
        self.analyzer = IdentifiabilityAnalyzer(
            state_names=state_names,
            parameter_names=parameter_names,
            observed_states=['q_agent', 'p_market']  # Agent holdings + Market price
        )

    def get_default_parameters(self) -> PHSParameters:
        """Return physically meaningful default parameters for Phase 1A system."""
        phase1a_params = Phase1AParameters(
            # Agent parameters (what we want to identify)
            k_agent=2.0,      # Agent stiffness
            m_agent=1.0,      # Agent mass
            r_agent=0.1,      # Agent friction
            
            # Market parameters
            k_market=1.5,     # Market stiffness
            m_market=0.8,     # Market mass  
            r_market=0.2,     # Market friction
            
            # Coupling parameters
            r_coupling=0.05,  # Bidirectional coupling strength
            
            # Noise parameters
            sigma_market=0.1, # Market noise intensity
        )
        return phase1a_params.to_phs_parameters()

    def hamiltonian_matrix(self, params: PHSParameters) -> jnp.ndarray:
        """
        Construct Hamiltonian matrix M for quadratic H = 0.5 * z^T * M * z.
        
        For Phase 1A: M = diag([k_agent, 1/m_agent, k_market, 1/m_market])
        """
        hp = params.hamiltonian_params
        
        # Add small epsilon for numerical stability
        m_agent_safe = hp['m_agent'] + 1e-8
        m_market_safe = hp['m_market'] + 1e-8
        
        return jnp.diag(jnp.array([
            hp['k_agent'],
            1.0 / m_agent_safe,
            hp['k_market'],
            1.0 / m_market_safe,
        ]))

    def hamiltonian_gradient(self, z: jnp.ndarray, params: PHSParameters) -> jnp.ndarray:
        """Compute Hamiltonian gradient ∇H(z) = M * z analytically."""
        M = self.hamiltonian_matrix(params)
        return M @ z

    def dissipation_matrix(self, z: jnp.ndarray, params: PHSParameters) -> jnp.ndarray:
        """
        Construct dissipation matrix R with bidirectional coupling.
        
        R structure for Phase 1A:
        R = [[0,   0,   0,   0  ],
             [0,   r_a, 0,   r_c],
             [0,   0,   0,   0  ],
             [0,   r_c, 0,   r_m]]
             
        This creates bidirectional coupling between agent and market momentum.
        """
        dp = params.dissipation_params
        
        return jnp.array([
            [0.0, 0.0, 0.0, 0.0],
            [0.0, dp['r_agent'], 0.0, dp['r_coupling']],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, dp['r_coupling'], 0.0, dp['r_market']],
        ])

    def noise_matrix(self, z: jnp.ndarray, params: PHSParameters) -> jnp.ndarray:
        """
        Noise coupling matrix for Phase 1A market excitation.
        
        External order flow enters only through market price (p_market).
        """
        cp = params.coupling_params
        return self.get_market_noise_matrix(cp['sigma_market'])

    def run_gateway_analysis(self, params: Optional[PHSParameters] = None) -> Dict[str, Any]:
        """
        Run complete Phase 1A gateway analysis using symbolic components.
        
        This is the critical theoretical validation that must pass before
        numerical identification can proceed.
        
        Args:
            params: System parameters (uses default if None)
            
        Returns:
            Complete gateway analysis results with pass/fail decision
        """
        if params is None:
            params = self.get_default_parameters()
            
        print("=== Phase 1A Linear De-coupling Gateway Analysis ===")
        print("Objective: Validate theoretical identifiability conditions")
        
        # Get symbolic matrices
        M_sym = self.symbolic_hamiltonian.symbolic_matrix
        R_sym = self.symbolic_dissipation.symbolic_matrix
        
        # Construct canonical J matrix symbolically
        # For state vector z = [q_agent, p_agent, q_market, p_market]
        # Standard symplectic structure: J = [[0, I], [-I, 0]]
        J_sym = sp.Matrix([
            [0, 1, 0, 0],    # dq_agent/dt = p_agent/m_agent
            [-1, 0, 0, 0],   # dp_agent/dt = -k_agent*q_agent
            [0, 0, 0, 1],    # dq_market/dt = p_market/m_market  
            [0, 0, -1, 0]    # dp_market/dt = -k_market*q_market
        ])
        
        # Build symbolic state matrix A = (J - R) * M
        A_sym = (J_sym - R_sym) * M_sym
        
        # Run complete gateway analysis
        results = self.analyzer.gateway_analysis(
            A_symbolic=A_sym,
            M_symbolic=M_sym,
            R_symbolic=R_sym,
            noise_targets=['p_market']
        )
        
        # Print results
        print(self.analyzer.generate_gateway_report())
        
        return results

    def simulate_stochastic_trajectory(self,
                                     params: PHSParameters,
                                     z0: jnp.ndarray,
                                     dt: float = 0.01,
                                     T: float = 10.0,
                                     key: Optional[jnp.ndarray] = None) -> Dict[str, jnp.ndarray]:
        """
        Simulate stochastic PHS trajectory for Phase 1A system.
        
        Generates synthetic data for PEM identification validation.
        Uses Euler-Maruyama integration for the SDE:
        dz = (J-R)M*z*dt + E*dW(t)
        
        Args:
            params: System parameters
            z0: Initial state [q_agent, p_agent, q_market, p_market]
            dt: Time step
            T: Final time
            key: JAX random key (generates if None)
            
        Returns:
            Dictionary with time series data for identification
        """
        if key is None:
            key = jax.random.PRNGKey(42)
            
        # Time grid
        t_grid = jnp.arange(0, T, dt)
        N = len(t_grid)
        
        # Initialize trajectory arrays
        z_traj = jnp.zeros((N, self.state_dim))
        z_traj = z_traj.at[0].set(z0)
        
        # System matrices
        J = self.J
        M = self.hamiltonian_matrix(params)
        noise_E = self.noise_matrix(jnp.zeros(self.state_dim), params)
        
        # Simulation loop
        current_z = z0
        for i in range(1, N):
            # Current system matrices (z-dependent)
            R = self.dissipation_matrix(current_z, params)
            
            # Deterministic drift: (J-R)M*z
            drift = (J - R) @ M @ current_z
            
            # Stochastic term: E*dW
            dW = jax.random.normal(key, (noise_E.shape[1],)) * jnp.sqrt(dt)
            key, _ = jax.random.split(key)
            
            noise_term = noise_E @ dW
            
            # Euler-Maruyama step
            current_z = current_z + drift * dt + noise_term
            z_traj = z_traj.at[i].set(current_z)
        
        # Extract observations (typically only positions)
        q_agent = z_traj[:, 0]
        p_agent = z_traj[:, 1] 
        q_market = z_traj[:, 2]
        p_market = z_traj[:, 3]
        
        return {
            'time': t_grid,
            'full_state': z_traj,
            'q_agent': q_agent,
            'p_agent': p_agent,
            'q_market': q_market,
            'p_market': p_market,
            'observations': z_traj[:, [0, 3]],  # q_agent and p_market observed
        }

    def validate_identification_capability(self,
                                         params_true: PHSParameters,
                                         simulation_time: float = 20.0,
                                         noise_level: float = 0.01) -> Dict[str, Any]:
        """
        Complete Phase 1A validation workflow: Gateway + Simulation.
        
        This function provides end-to-end validation of the Phase 1A system:
        1. Theoretical gateway analysis (observability/identifiability)
        2. Stochastic trajectory generation
        3. Data preparation for PEM identification
        
        Args:
            params_true: Ground truth parameters
            simulation_time: Length of simulation for data generation
            noise_level: Additional measurement noise level
            
        Returns:
            Complete validation results including gateway analysis and data
        """
        results = {
            'gateway_analysis': {},
            'simulation_data': {},
            'identification_setup': {},
            'validation_summary': {}
        }
        
        # Step 1: Gateway Analysis
        print("Step 1: Running theoretical gateway analysis...")
        gateway_results = self.run_gateway_analysis(params_true)
        results['gateway_analysis'] = gateway_results
        
        # Step 2: Check if gateway passed
        gateway_passed = gateway_results['gateway_passed']
        
        if not gateway_passed:
            results['validation_summary'] = {
                'success': False,
                'message': 'Gateway analysis failed - theoretical conditions not met',
                'recommendations': gateway_results['recommendations']
            }
            return results
        
        # Step 3: Generate synthetic data
        print("Step 2: Gateway passed! Generating synthetic trajectory data...")
        
        # Initial conditions
        z0 = jnp.array([0.1, 0.0, -0.05, 0.0])  # Small perturbation from equilibrium
        
        # Simulate trajectory
        sim_data = self.simulate_stochastic_trajectory(
            params=params_true,
            z0=z0,
            dt=0.01,
            T=simulation_time
        )
        results['simulation_data'] = sim_data
        
        # Step 4: Prepare for PEM identification
        print("Step 3: Preparing data for PEM identification...")
        
        # Add measurement noise to observations
        key = jax.random.PRNGKey(123)
        measurement_noise = jax.random.normal(
            key, sim_data['observations'].shape
        ) * noise_level
        
        noisy_observations = sim_data['observations'] + measurement_noise
        
        results['identification_setup'] = {
            'observations': noisy_observations,
            'time_grid': sim_data['time'],
            'true_parameters': {
                'k_agent': params_true.hamiltonian_params['k_agent'],
                'm_agent': params_true.hamiltonian_params['m_agent'],
                'r_agent': params_true.dissipation_params['r_agent'],
            },
            'measurement_noise_level': noise_level,
            'data_statistics': {
                'n_observations': len(sim_data['time']),
                'observation_dimension': noisy_observations.shape[1],
                'simulation_time': simulation_time,
            }
        }
        
        # Step 5: Validation summary
        results['validation_summary'] = {
            'success': True,
            'gateway_passed': True,
            'data_generated': True,
            'ready_for_pem': True,
            'message': 'Phase 1A validation complete - ready for PEM identification',
            'next_steps': [
                'Implement PEM optimizer using prepared data',
                'Run parameter identification with multiple initial guesses',
                'Validate recovery of agent parameters against ground truth',
                'Proceed to Phase 1B if identification succeeds'
            ]
        }
        
        print("=== Phase 1A Validation Complete ===")
        print(f"Gateway Status: {'✅ PASSED' if gateway_passed else '❌ FAILED'}")
        print(f"Data Generated: {len(sim_data['time'])} time points")
        print(f"Ready for PEM: {'Yes' if results['validation_summary']['ready_for_pem'] else 'No'}")
        
        return results

    # Additional utility methods for Phase 1A analysis
    
    def get_system_eigenvalues(self, params: PHSParameters) -> jnp.ndarray:
        """Compute system eigenvalues for stability analysis."""
        A_num, _ = self.get_state_space_matrices(params)
        return jnp.linalg.eigvals(A_num)

    def compute_energy_balance(self, 
                             z_trajectory: jnp.ndarray,
                             params: PHSParameters) -> Dict[str, jnp.ndarray]:
        """
        Analyze energy flow in the coupled system.
        
        Provides insight into energy transfer between agent and market.
        """
        M = self.hamiltonian_matrix(params)
        
        # Hamiltonian (total energy) along trajectory
        H_traj = jnp.array([0.5 * z.T @ M @ z for z in z_trajectory])
        
        # Energy dissipation analysis would require trajectory derivatives
        # This is a simplified version for demonstration
        
        return {
            'total_energy': H_traj,
            'agent_kinetic': 0.5 * z_trajectory[:, 1]**2 / params.hamiltonian_params['m_agent'],
            'agent_potential': 0.5 * params.hamiltonian_params['k_agent'] * z_trajectory[:, 0]**2,
            'market_kinetic': 0.5 * z_trajectory[:, 3]**2 / params.hamiltonian_params['m_market'],
            'market_potential': 0.5 * params.hamiltonian_params['k_market'] * z_trajectory[:, 2]**2,
        }
#!/usr/bin/env python3
"""
Demonstration of the refined Hamiltonian architecture.

This script shows the clean separation between symbolic analysis 
and numerical computation enabled by the new design.
"""

import jax.numpy as jnp
from src.phynance.core.hamiltonian_base import SymbolicQuadraticHamiltonian, QuadraticHamiltonian


def test_symbolic_to_numeric_workflow():
    """Demonstrate Phase 1A workflow: symbolic analysis → numerical simulation."""
    
    print("=== Phase 1A Workflow Demonstration ===\n")
    
    # Step 1: Symbolic Analysis for Identifiability
    print("1. Creating symbolic Hamiltonian for identifiability analysis...")
    symbolic_ham = SymbolicQuadraticHamiltonian()
    
    print(f"   State variables: {symbolic_ham.state_names}")
    print(f"   Parameters: {symbolic_ham.param_names}")
    print(f"   Symbolic M matrix:\n{symbolic_ham.symbolic_matrix}")
    print(f"   Symbolic Hamiltonian: {symbolic_ham.symbolic_hamiltonian}")
    
    # Step 2: Create numerical instance for simulation
    print("\n2. Creating numerical Hamiltonian for simulation...")
    
    # True parameters for synthetic data generation
    θ_true = {
        'k_agent': 2.0,
        'm_agent': 1.5, 
        'k_market': 3.0,
        'm_market': 2.0
    }
    
    # Use factory method to create numerical instance
    numerical_ham = symbolic_ham.to_numeric(**θ_true)
    
    print(f"   k_agent: {numerical_ham.k_agent}")
    print(f"   m_agent: {numerical_ham.m_agent}")
    print(f"   k_market: {numerical_ham.k_market}")
    print(f"   m_market: {numerical_ham.m_market}")
    
    # Step 3: Test numerical operations
    print("\n3. Testing numerical operations...")
    
    # Test state
    z_test = jnp.array([1.0, 0.5, -0.8, 0.3])  # [q_agent, p_agent, q_market, p_market]
    
    print(f"   Test state z: {z_test}")
    print(f"   H(z): {numerical_ham.value(z_test):.6f}")
    print(f"   ∇H(z): {numerical_ham.gradient(z_test)}")
    print(f"   Is convex: {numerical_ham.is_convex(z_test)}")
    
    # Step 4: Show PEM-ready optimization setup
    print("\n4. PEM optimization setup...")
    
    # Initial guess parameters (what optimizer will start from)
    θ_guess = {
        'k_agent': 1.8,
        'm_agent': 1.2,
        'k_market': 2.5,
        'm_market': 1.8
    }
    
    # Create Hamiltonian for optimization (this is what optimistix will modify)
    ham_for_optimization = symbolic_ham.to_numeric(**θ_guess)
    
    print("   Initial guess parameters:")
    print(f"     k_agent: {ham_for_optimization.k_agent}")
    print(f"     m_agent: {ham_for_optimization.m_agent}")
    print(f"     k_market: {ham_for_optimization.k_market}")
    print(f"     m_market: {ham_for_optimization.m_market}")
    
    print("\n   Optimistix will directly optimize these parameters as JAX PyTree leaves!")
    
    # Step 5: Demonstrate numerical stability
    print("\n5. Testing numerical stability...")
    
    # Test with very small mass (should be handled gracefully)
    small_mass_ham = symbolic_ham.to_numeric(
        k_agent=1.0, m_agent=1e-10, k_market=1.0, m_market=1.0
    )
    
    print(f"   With tiny m_agent (1e-10): H(z) = {small_mass_ham.value(z_test):.6f}")
    print(f"   Still convex: {small_mass_ham.is_convex(z_test)}")


def test_direct_numerical_usage():
    """Show direct numerical usage when symbolic analysis isn't needed."""
    
    print("\n=== Direct Numerical Usage ===\n")
    
    # Direct creation for fast simulation (no symbolic overhead)
    direct_ham = QuadraticHamiltonian(
        k_agent=1.0, m_agent=2.0, k_market=1.5, m_market=2.5
    )
    
    z = jnp.array([0.1, 0.2, -0.1, 0.05])
    
    print("Direct numerical Hamiltonian:")
    print(f"  H(z): {direct_ham.value(z):.6f}")
    print(f"  ∇H(z): {direct_ham.gradient(z)}")
    print(f"  M matrix:\n{direct_ham._build_M_matrix()}")


if __name__ == "__main__":
    test_symbolic_to_numeric_workflow()
    test_direct_numerical_usage()
    print("\n✓ All tests completed successfully!")

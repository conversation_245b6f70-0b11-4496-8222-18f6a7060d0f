"""
Global pytest configuration for the phynance test suite.
"""

import os

import pytest


def pytest_configure(config):
    """Configure JAX for testing."""
    os.environ['JAX_ENABLE_X64'] = 'True'


@pytest.fixture
def sample_parameters():
    """Sample parameters for Hamiltonian testing."""
    return {
        'k_agent': 2.0,
        'm_agent': 1.5,
        'k_market': 3.0,
        'm_market': 2.0
    }

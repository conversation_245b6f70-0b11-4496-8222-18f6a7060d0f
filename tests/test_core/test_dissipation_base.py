"""
Unit tests for Dissipation base classes.

Tests the core dissipation architecture for Phase 1A coupled systems
and the symbolic-to-numerical workflow for parameter estimation.
"""

import jax.numpy as jnp
import pytest

from phynance.core.dissipation_base import (
    DissipationBase,
    SymbolicDissipationMatrix,
    CoupledDissipation,
    PolynomialDissipation,
)


@pytest.fixture
def sample_dissipation_params():
    """Sample dissipation parameters for testing."""
    return {
        'r_agent': 0.1,
        'r_market': 0.05,
        'r_coupling': 0.02
    }


class TestSymbolicDissipationMatrix:
    """Test the symbolic dissipation matrix for Phase 1A identifiability analysis."""

    def test_initialization(self):
        """Test symbolic dissipation creates correct structure."""
        dissip = SymbolicDissipationMatrix()
        
        # Check basic structure
        assert dissip.state_names == ['q_agent', 'p_agent', 'q_market', 'p_market']
        assert dissip.param_names == ['r_agent', 'r_market', 'r_coupling']
        assert dissip.symbolic_matrix.shape == (4, 4)

    def test_coupling_structure(self):
        """Test symbolic matrix has correct coupling structure."""
        dissip = SymbolicDissipationMatrix()
        R = dissip.symbolic_matrix
        
        # Check momentum coupling: (1,3) and (3,1) should have r_coupling
        assert str(R[1, 3]) == 'r_coupling'
        assert str(R[3, 1]) == 'r_coupling'
        
        # Check diagonal friction terms
        assert str(R[1, 1]) == 'r_agent'
        assert str(R[3, 3]) == 'r_market'

    def test_to_numeric_factory(self, sample_dissipation_params):
        """Test factory method creates numerical instances."""
        symbolic_dissip = SymbolicDissipationMatrix()
        numerical_dissip = symbolic_dissip.to_numeric(**sample_dissipation_params)
        
        assert isinstance(numerical_dissip, CoupledDissipation)
        assert float(numerical_dissip.r_agent) == sample_dissipation_params['r_agent']


class TestCoupledDissipation:
    """Test the numerical coupled dissipation for Phase 1A simulations."""

    def test_matrix_structure(self, sample_dissipation_params):
        """Test dissipation matrix has correct structure."""
        dissip = CoupledDissipation(**sample_dissipation_params)
        R = dissip.matrix()
        
        assert R.shape == (4, 4)
        
        # Check coupling terms
        assert float(R[1, 3]) == sample_dissipation_params['r_coupling']
        assert float(R[3, 1]) == sample_dissipation_params['r_coupling']
        
        # Check diagonal friction
        assert float(R[1, 1]) == sample_dissipation_params['r_agent']
        assert float(R[3, 3]) == sample_dissipation_params['r_market']

    def test_positive_semidefinite_valid(self):
        """Test PSD check with valid parameters."""
        dissip = CoupledDissipation(r_agent=0.1, r_market=0.1, r_coupling=0.01)
        assert dissip.is_positive_semidefinite()

    def test_positive_semidefinite_invalid(self):
        """Test PSD check with invalid parameters."""
        # Too strong coupling violates PSD constraint
        dissip = CoupledDissipation(r_agent=0.01, r_market=0.01, r_coupling=0.1)
        assert not dissip.is_positive_semidefinite()

    def test_symmetry(self, sample_dissipation_params):
        """Test dissipation matrix is symmetric."""
        dissip = CoupledDissipation(**sample_dissipation_params)
        R = dissip.matrix()
        
        assert jnp.allclose(R, R.T)

    def test_state_independence(self, sample_dissipation_params):
        """Test matrix is independent of state for Phase 1A."""
        dissip = CoupledDissipation(**sample_dissipation_params)
        z1 = jnp.array([1.0, 0.5, -0.8, 0.3])
        z2 = jnp.array([0.2, -1.0, 0.5, -0.1])
        
        R1 = dissip.matrix(z1)
        R2 = dissip.matrix(z2)
        R_none = dissip.matrix()
        
        assert jnp.allclose(R1, R2)
        assert jnp.allclose(R1, R_none)


class TestPhase1AWorkflow:
    """Test the complete Phase 1A workflow: symbolic → numerical → validation."""

    def test_symbolic_to_numerical_workflow(self, sample_dissipation_params):
        """Test the factory pattern for Phase 1A parameter estimation."""
        # Step 1: Symbolic analysis
        symbolic_dissip = SymbolicDissipationMatrix()
        
        # Step 2: Create numerical instances
        true_dissip = symbolic_dissip.to_numeric(**sample_dissipation_params)
        guess_params = {k: v * 0.8 for k, v in sample_dissipation_params.items()}
        guess_dissip = symbolic_dissip.to_numeric(**guess_params)
        
        # Step 3: Verify they produce different matrices
        true_R = true_dissip.matrix()
        guess_R = guess_dissip.matrix()
        
        assert not jnp.allclose(true_R, guess_R)

    def test_jax_compatibility(self, sample_dissipation_params):
        """Test JAX compatibility for parameter optimization."""
        import jax
        
        def energy_dissipation(params, z, dt=0.01):
            dissip = CoupledDissipation(**params)
            R = dissip.matrix()
            # Energy dissipation rate: -z^T R z
            return -jnp.dot(z, jnp.dot(R, z)) * dt
        
        z = jnp.array([1.0, 0.5, -0.8, 0.3])
        jax_params = {k: jnp.array(v) for k, v in sample_dissipation_params.items()}
        
        # Should work with JAX transformations
        param_grads = jax.grad(energy_dissipation)(jax_params, z)
        assert all(k in param_grads for k in sample_dissipation_params.keys())


class TestAbstractInterfaces:
    """Test abstract base classes for future extensibility."""

    def test_dissipation_base_is_abstract(self):
        """Test that DissipationBase cannot be instantiated."""
        with pytest.raises(TypeError):
            DissipationBase()

    def test_polynomial_dissipation_placeholder(self):
        """Test Phase 1B placeholder is properly defined."""
        poly_dissip = PolynomialDissipation()
        z = jnp.array([1.0, 0.0, 0.0, 0.0])
        
        with pytest.raises(NotImplementedError):
            poly_dissip.matrix(z)


class TestPhysicalConstraints:
    """Test physical realizability constraints."""

    def test_energy_dissipation_nonpositive(self):
        """Test that energy dissipation is always non-positive."""
        dissip = CoupledDissipation(r_agent=0.1, r_market=0.05, r_coupling=0.02)
        R = dissip.matrix()
        
        # Test multiple state vectors
        for _ in range(10):
            z = jnp.array([1.0, 2.0, -1.5, 0.8])  # Non-zero momentum
            energy_rate = -jnp.dot(z, jnp.dot(R, z))
            assert float(energy_rate) <= 0  # Energy can only dissipate
"""
Unit tests for Hamiltonian base classes.

Tests the core Hamiltonian architecture for Phase 1A quadratic systems
and the symbolic-to-numerical workflow for parameter estimation.
"""

import jax.numpy as jnp
import pytest

from phynance.core.hamiltonian_base import (
    HamiltonianBase,
    PolynomialHamiltonian,
    QuadraticHamiltonian,
    SymbolicQuadraticHamiltonian,
)


class TestSymbolicQuadraticHamiltonian:
    """Test the symbolic Hamiltonian for Phase 1A identifiability analysis."""

    def test_initialization(self):
        """Test symbolic Hamiltonian creates correct structure."""
        ham = SymbolicQuadraticHamiltonian()
        
        # Check basic structure
        assert ham.state_names == ['q_agent', 'p_agent', 'q_market', 'p_market']
        assert ham.param_names == ['k_agent', 'm_agent', 'k_market', 'm_market']
        assert ham.symbolic_matrix.shape == (4, 4)

    def test_to_numeric_factory(self, sample_parameters):
        """Test factory method creates numerical instances."""
        symbolic_ham = SymbolicQuadraticHamiltonian()
        numerical_ham = symbolic_ham.to_numeric(**sample_parameters)
        
        assert isinstance(numerical_ham, QuadraticHamiltonian)
        assert float(numerical_ham.k_agent) == sample_parameters['k_agent']


class TestQuadraticHamiltonian:
    """Test the numerical Hamiltonian for Phase 1A simulations."""

    def test_value_computation(self, sample_parameters):
        """Test Hamiltonian value computation."""
        ham = QuadraticHamiltonian(**sample_parameters)
        z = jnp.array([1.0, 0.5, -0.8, 0.3])
        
        # Should return finite positive value
        H_val = ham.value(z)
        assert jnp.isfinite(H_val)
        assert float(H_val) > 0

    def test_gradient_computation(self, sample_parameters):
        """Test gradient computation for PHS dynamics."""
        ham = QuadraticHamiltonian(**sample_parameters)
        z = jnp.array([1.0, 0.5, -0.8, 0.3])
        
        grad = ham.gradient(z)
        assert grad.shape == (4,)
        assert jnp.all(jnp.isfinite(grad))

    def test_convexity_check(self, sample_parameters):
        """Test convexity for optimization."""
        ham = QuadraticHamiltonian(**sample_parameters)
        z = jnp.array([1.0, 0.5, -0.8, 0.3])
        
        assert ham.is_convex(z)  # Positive parameters should be convex


class TestPhase1AWorkflow:
    """Test the complete Phase 1A workflow: symbolic → numerical → PEM."""

    def test_symbolic_to_numerical_workflow(self, sample_parameters):
        """Test the factory pattern for Phase 1A parameter estimation."""
        # Step 1: Symbolic analysis
        symbolic_ham = SymbolicQuadraticHamiltonian()
        
        # Step 2: Create numerical instances for true and guess parameters
        true_ham = symbolic_ham.to_numeric(**sample_parameters)
        guess_params = {k: v * 0.9 for k, v in sample_parameters.items()}  # 10% off
        guess_ham = symbolic_ham.to_numeric(**guess_params)
        
        # Step 3: Verify they produce different dynamics (for PEM validation)
        z = jnp.array([1.0, 0.5, -0.8, 0.3])
        true_val = float(true_ham.value(z))
        guess_val = float(guess_ham.value(z))
        
        assert abs(true_val - guess_val) > 1e-3  # Should be distinguishable

    def test_jax_compatibility_for_optimization(self, sample_parameters):
        """Test JAX compatibility for parameter optimization in PEM."""
        import jax
        
        def loss_fn(params):
            ham = QuadraticHamiltonian(**params)
            z = jnp.array([1.0, 0.0, 0.0, 0.0])
            return ham.value(z)
        
        # Convert to JAX arrays for optimization
        jax_params = {k: jnp.array(v) for k, v in sample_parameters.items()}
        
        # Should work with JAX transformations
        param_grads = jax.grad(loss_fn)(jax_params)
        assert all(k in param_grads for k in sample_parameters.keys())


class TestAbstractInterfaces:
    """Test abstract base classes for future extensibility."""

    def test_hamiltonian_base_is_abstract(self):
        """Test that HamiltonianBase cannot be instantiated."""
        with pytest.raises(TypeError):
            HamiltonianBase()

    def test_polynomial_hamiltonian_placeholder(self):
        """Test Phase 1B placeholder is properly defined."""
        poly_ham = PolynomialHamiltonian()
        z = jnp.array([1.0, 0.0, 0.0, 0.0])
        
        with pytest.raises(NotImplementedError):
            poly_ham.value(z)

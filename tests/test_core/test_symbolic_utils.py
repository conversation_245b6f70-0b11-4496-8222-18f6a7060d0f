"""
Tests for symbolic_utils module - Phase 1A symbolic analysis functions.

Tests the core symbolic analysis functions needed for the Linear De-coupling Gateway
theoretical validation.
"""

import pytest
import sympy as sp
import numpy as np
from sympy import symbols, Matrix, eye, zeros

from phynance.core.symbolic_utils import (
    construct_phs_state_matrix,
    construct_observability_matrix,
    check_observability_rank,
    construct_transfer_function,
    check_controllability_rank,
    analyze_structural_identifiability,
    extract_parameter_constraints,
    construct_noise_coupling_matrix,
)


class TestBasicConstructions:
    """Test basic matrix construction functions."""

    def test_construct_phs_state_matrix(self):
        """Test PHS state matrix construction A = (J-R)M."""
        # Simple 2x2 example
        J = Matrix([[0, 1], [-1, 0]])
        R = Matrix([[0, 0], [0, 1]])
        M = Matrix([[2, 0], [0, 0.5]])

        A = construct_phs_state_matrix(J, R, M)
        
        # Verify shape and structure
        assert A.shape == (2, 2)
        # A should be (J-R)M = [[0, 1], [-1, -1]] * [[2, 0], [0, 0.5]]
        expected = Matrix([[0, 0.5], [-2, -0.5]])
        assert A.equals(expected)

    def test_construct_observability_matrix(self):
        """Test observability matrix construction O = [C; CA; CA²; ...]."""
        A = Matrix([[0, 1], [-2, -3]])
        C = Matrix([[1, 0]])
        
        O = construct_observability_matrix(A, C, 2)
        
        # O should be [C; CA] = [[1, 0]; [0, 1]]
        expected = Matrix([[1, 0], [0, 1]])
        assert O.equals(expected)

    def test_construct_transfer_function(self):
        """Test transfer function construction G(s) = C(sI-A)^(-1)."""
        s = symbols('s')
        A = Matrix([[0, 1], [-2, -3]])
        C = Matrix([[1, 0]])
        
        G = construct_transfer_function(A, C, s)
        
        # G(s) should be a 1x2 matrix (since C is 1x2 and (sI-A)^(-1) is 2x2)
        assert G.shape == (1, 2)
        # Verify it's a rational function in s
        assert G[0, 0].is_rational_function(s)

    def test_construct_noise_coupling_matrix(self):
        """Test noise coupling matrix construction for Phase 1A."""
        state_names = ['q_agent', 'p_agent', 'q_market', 'p_market']
        
        # Default: noise only on p_market
        E = construct_noise_coupling_matrix(state_names)
        
        assert E.shape == (4, 1)
        # Only p_market (index 3) should have noise
        assert E[3, 0] == symbols('sigma_p_market')
        for i in range(3):
            assert E[i, 0] == 0

        # Custom noise targets
        E_custom = construct_noise_coupling_matrix(
            state_names, 
            noise_targets=['p_agent', 'p_market']
        )
        
        assert E_custom.shape == (4, 2)
        assert E_custom[1, 0] == symbols('sigma_p_agent')
        assert E_custom[3, 1] == symbols('sigma_p_market')


class TestObservabilityAnalysis:
    """Test observability analysis functions."""

    def test_check_observability_rank_observable(self):
        """Test observability check on fully observable system."""
        A = Matrix([[0, 1], [-2, -3]])
        C = Matrix([[1, 0]])
        O = construct_observability_matrix(A, C, 2)
        
        is_obs, rank = check_observability_rank(O)
        
        assert is_obs is True
        assert rank == 2

    def test_check_observability_rank_unobservable(self):
        """Test observability check on unobservable system."""
        # Construct unobservable system
        A = Matrix([[1, 0], [0, 2]])
        C = Matrix([[1, 0]])  # Can only see first state
        O = construct_observability_matrix(A, C, 2)
        
        is_obs, rank = check_observability_rank(O)
        
        assert is_obs is False
        assert rank == 1  # Only rank 1 instead of 2


class TestControllabilityAnalysis:
    """Test controllability analysis functions."""

    def test_check_controllability_rank_controllable(self):
        """Test controllability check on fully controllable system."""
        A = Matrix([[0, 1], [-2, -3]])
        B = Matrix([[0], [1]])
        
        is_ctrl, rank = check_controllability_rank(A, B)
        
        assert is_ctrl is True
        assert rank == 2

    def test_check_controllability_rank_uncontrollable(self):
        """Test controllability check on uncontrollable system."""
        # Diagonal system with input only to first state
        A = Matrix([[1, 0], [0, 2]]) 
        B = Matrix([[1], [0]])
        
        is_ctrl, rank = check_controllability_rank(A, B)
        
        assert is_ctrl is False
        assert rank == 1


class TestIdentifiabilityAnalysis:
    """Test structural identifiability analysis."""

    def test_analyze_structural_identifiability_simple(self):
        """Test identifiability analysis on simple transfer function."""
        s = symbols('s')
        k, m = symbols('k m', positive=True)
        
        # Simple transfer function G(s) = k/(s² + s/m + k)
        G = Matrix([[k / (s**2 + s/m + k)]])
        
        result = analyze_structural_identifiability(G, [k, m], s)
        
        assert isinstance(result, dict)
        assert 'identifiable' in result
        assert 'coefficients' in result
        assert 'parameter_mapping' in result
        assert 'constraints' in result

    def test_analyze_structural_identifiability_phase1a_system(self):
        """Test identifiability on Phase 1A coupled system structure."""
        s = symbols('s')
        # Phase 1A parameters
        k_a, m_a, k_m, m_m, r_a, r_m, r_c = symbols(
            'k_agent m_agent k_market m_market r_agent r_market r_coupling',
            positive=True
        )
        
        # Simple 1-input 1-output transfer function for testing
        # This is a simplified version for unit testing
        num = k_a * k_m
        den = (s**2 + r_a*s + k_a) * (s**2 + r_m*s + k_m) - r_c**2
        G = Matrix([[num / den]])
        
        parameters = [k_a, m_a, k_m, m_m, r_a, r_m, r_c]
        result = analyze_structural_identifiability(G, parameters, s)
        
        # Should have analysis results even if not fully identifiable
        assert len(result['coefficients']) > 0
        assert len(result['constraints']) >= 0


class TestParameterConstraints:
    """Test parameter constraint extraction."""

    def test_extract_parameter_constraints_phase1a(self):
        """Test constraint extraction for Phase 1A system."""
        # Phase 1A Hamiltonian matrix (diagonal)
        k_a, m_a, k_m, m_m = symbols('k_agent m_agent k_market m_market')
        H = sp.diag(k_a, 1/m_a, k_m, 1/m_m)
        
        # Phase 1A dissipation matrix (with coupling)
        r_a, r_m, r_c = symbols('r_agent r_market r_coupling')
        R = Matrix([
            [0, 0, 0, 0],
            [0, r_a, 0, r_c],
            [0, 0, 0, 0],
            [0, r_c, 0, r_m]
        ])
        
        parameters = [k_a, m_a, k_m, m_m, r_a, r_m, r_c]
        constraints = extract_parameter_constraints(H, R, parameters)
        
        assert len(constraints) > 0
        # Should have positivity constraints for k and m parameters
        k_constraints = [c for c in constraints if 'k_' in c]
        m_constraints = [c for c in constraints if 'm_' in c]
        assert len(k_constraints) >= 2  # k_agent, k_market
        assert len(m_constraints) >= 2  # m_agent, m_market


class TestPhase1AIntegration:
    """Integration tests combining multiple functions for Phase 1A workflow."""

    def test_phase1a_symbolic_workflow(self):
        """Test complete Phase 1A symbolic analysis workflow."""
        # Define Phase 1A system symbolically
        state_names = ['q_agent', 'p_agent', 'q_market', 'p_market']
        k_a, m_a, k_m, m_m = symbols('k_agent m_agent k_market m_market')
        r_a, r_m, r_c = symbols('r_agent r_market r_coupling')
        
        # Construct system matrices
        # Hamiltonian matrix M
        M = sp.diag(k_a, 1/m_a, k_m, 1/m_m)
        
        # Canonical J matrix
        J = Matrix([
            [0, 0, 1, 0],
            [0, 0, 0, 1], 
            [-1, 0, 0, 0],
            [0, -1, 0, 0]
        ])
        
        # Dissipation matrix R (with coupling)
        R = Matrix([
            [0, 0, 0, 0],
            [0, r_a, 0, r_c],
            [0, 0, 0, 0],
            [0, r_c, 0, r_m]
        ])
        
        # State matrix A = (J-R)M
        A = (J - R) * M
        
        # Observation matrix (observe positions only)
        C = Matrix([
            [1, 0, 0, 0],  # q_agent
            [0, 0, 1, 0]   # q_market
        ])
        
        # Noise coupling (external order flow to p_market)
        E = construct_noise_coupling_matrix(state_names)
        
        # Test observability
        O = construct_observability_matrix(A, C, 4)
        is_obs, obs_rank = check_observability_rank(O)
        
        # Test controllability (with noise input)
        is_ctrl, ctrl_rank = check_controllability_rank(A, E)
        
        # Verify basic properties
        assert A.shape == (4, 4)
        assert C.shape == (2, 4)
        assert E.shape == (4, 1)
        assert O.shape == (8, 4)  # 2 outputs × 4 time steps
        
        # Should have some observability and controllability
        # (exact values depend on symbolic computation)
        assert obs_rank >= 1
        assert ctrl_rank >= 1
        
        # Test constraint extraction
        parameters = [k_a, m_a, k_m, m_m, r_a, r_m, r_c]
        constraints = extract_parameter_constraints(M, R, parameters)
        assert len(constraints) > 0

    def test_phase1a_transfer_function_construction(self):
        """Test transfer function construction for Phase 1A system."""
        s = symbols('s')
        
        # Simple 2x2 system for testing transfer function
        A = Matrix([[0, 1], [-2, -3]])
        C = Matrix([[1, 0]])
        
        G = construct_transfer_function(A, C, s)
        
        # Should be 1x2 transfer function (C is 1x2, A is 2x2)
        assert G.shape == (1, 2)
        
        # Test identifiability analysis on first element
        k, m = symbols('k m')
        parameters = [k, m]
        
        # Use just the first element as a scalar transfer function
        G_scalar = Matrix([[G[0, 0]]])
        result = analyze_structural_identifiability(G_scalar, parameters, s)
        assert isinstance(result, dict)